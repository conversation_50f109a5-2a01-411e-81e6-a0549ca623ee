{"name": "@web/source", "version": "0.0.0", "license": "MIT", "scripts": {"api:serve": "tsx watch --tsconfig ./apps/api/tsconfig.app.json ./apps/api/src/main.ts"}, "private": true, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/js": "21.3.11", "@nx/react": "^21.3.11", "@nx/react-native": "^21.3.11", "@nx/vite": "21.3.11", "@nx/web": "21.3.11", "@react-native-community/cli": "~15.0.1", "@react-native-community/cli-platform-android": "~15.0.1", "@react-native-community/cli-platform-ios": "~15.0.1", "@react-native/babel-preset": "~0.76.3", "@react-native/metro-config": "~0.76.3", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/jest-native": "~5.4.3", "@testing-library/react-native": "~12.9.0", "@types/node": "^20.0.0", "@types/react": "~18.3.12", "@types/react-dom": "~18.3.1", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^3.0.0", "autoprefixer": "10.4.13", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest-react-native": "18.0.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.3.11", "postcss": "8.4.38", "prettier": "^2.6.2", "react-native-svg": "~15.8.0", "react-native-svg-transformer": "~1.5.0", "react-native-svg-web": "~1.0.9", "react-native-web": "~0.19.13", "react-test-renderer": "~18.3.1", "tailwindcss": "3.4.3", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vitest": "^3.0.0"}, "workspaces": ["packages/*", "apps/*"], "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@hono/node-server": "^1.18.2", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.4", "fetch-cookie": "^3.1.0", "hono": "^4.9.1", "htm": "^3.1.1", "jsonwebtoken": "^9.0.2", "marked": "^16.1.2", "marked-xhtml": "^1.0.13", "mongoose": "^8.17.1", "mysql2": "^3.14.3", "nodemailer": "^7.0.5", "otpauth": "^9.4.1", "react": "~18.3.1", "react-dom": "~18.3.1", "react-native": "~0.76.3", "react-router-dom": "6.29.0", "rxjs": "^7.8.2"}}