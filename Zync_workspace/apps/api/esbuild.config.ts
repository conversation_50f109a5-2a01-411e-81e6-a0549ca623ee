import { build } from 'esbuild';
import { writeFileSync } from 'node:fs';
import { resolve } from 'node:path';
import { replaceFiles } from '../../tools/tim/esbuild-plugins';

const main = async (forServer = 'test') => {
  const t0 = performance.now();
  const root = resolve('./packages/api/src/');
  const output = await build({
    bundle: true,
    entryPoints: [resolve(root, './main.ts')],
    legalComments: 'none',
    loader: { '.ts': 'ts' },
    minify: true,
    external: [
      'mysql2',
      'csso',
      'nodemailer',
      '@aws-sdk/client-s3',
      '@aws-sdk/s3-request-presigner',
    ],
    outdir: resolve('./dist/packages/api'),
    target: 'es2022',
    platform: 'node',
    metafile: true,
    tsconfig: 'packages/api/tsconfig.app.json',
    plugins: [
      replaceFiles([
        {
          from: resolve(root, './environments/environment.ts'),
          to: resolve(root, `./environments/environment.${forServer}.ts`),
        },
        {
          from: resolve(root, './environments/env-modules.ts'),
          to: resolve(root, `./environments/env-modules.${forServer}.ts`),
        },
      ]),
    ],
  });

  writeFileSync(
    resolve('./dist/packages/api/metafile.json'),
    JSON.stringify(output.metafile, null, 2),
  );
  const t1 = performance.now();
  console.log(`Build: ${t1 - t0}ms`);
};

export default main;
