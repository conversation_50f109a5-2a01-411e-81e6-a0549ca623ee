import { AuthController } from './controllers/auth/auth.controller';
import { BusStopsController } from './controllers/bus-stops/bus-stops.controller';
import { CondoController } from './controllers/condo/condo.controller';
import { <PERSON>rController } from './controllers/hawker/hawker.controller';
import { HDBController } from './controllers/hdb/hdb.controller';
import { HospitalController } from './controllers/hospitals/hospital.controller';
import { MetroController } from './controllers/metro/metro.controller';
import { OneMapSearchController } from './controllers/one-map/one-map-search.controller';
import { UserSalesListingController } from './controllers/sales-listings/user-sales-listing.controller';
import { SchoolController } from './controllers/schools/school.controller';
import { SettingsController } from './controllers/settings/settings.controller';
import { HDBSalesController } from './controllers/hdb-sales/hdb-sales.controller';
import { HDBSalesOfferController } from './controllers/hdb-sales/hdb-sales-offer.controller';
import { SalesChatController } from './controllers/hdb-sales/sales-chat.controller';
import { HDBSalesTransactionController } from './controllers/hdb-sales/transaction-resale-progress.controller';

export const CONTROLLERS = {
  AuthController,
  BusStopsController,
  CondoController,
  HawkerController,
  HDBController,
  SchoolController,
  SettingsController,
  OneMapSearchController,
  HospitalController,
  MetroController,
  UserSalesListingController,
  HDBSalesController,
  HDBSalesOfferController,
  SalesChatController,
  HDBSalesTransactionController
};
