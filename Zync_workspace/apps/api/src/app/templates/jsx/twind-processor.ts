import { defineConfig, extract, setup, virtual } from '@twind/core';
import presetTailwind from '@twind/preset-tailwind';

const hash = (() => {
  let i = 1;
  const hasMap = {};
  return (x: string) => (hasMap[x] ??= `_${(i++).toString(36)}`);
})();

const tw = setup(
  {
    ...defineConfig({
      presets: [
        presetTailwind({ disablePreflight: true }),
        { theme: { fontFamily: { sans: ['Helvetica', 'sans-serif'], serif: ['Times', 'serif'] } } },
      ],
    }),
    hash,
  },
  () => (virtual as any)(false, false),
);

export const twindIfy = (body: string) => {
  const { html, css } = extract(body, tw);
  return { html, css };
};
