import { getConnInfo } from '@hono/node-server/conninfo';
import { HttpException } from '@lib/common/common.error';
import { HttpStatus } from '@lib/common/http-status';
import type { APIContext } from './guards';
import { SetMetaData, UseGuard, applyDecorators } from './guards';

const store = new Map<string, { count: number; last: number }>();

const RateLimitGuardKey = Symbol('RateLimitGuard');

export const RateLimit = (opt: { limit?: number; ttl?: number; skip?: boolean }) =>
  applyDecorators([SetMetaData(RateLimitGuardKey, opt), UseGuard(RateLimitGuard)]);

export const RateLimitGuard = ({ c, getMeta }: APIContext) => {
  const opt: { limit: number; ttl: number; skip?: boolean } = getMeta(RateLimitGuardKey);
  if (opt.skip) return;
  const key = getConnInfo(c).remote.address;
  const now = Date.now();
  if (!store.has(key)) store.set(key, { count: 0, last: now });
  const state = store.get(key)!;
  if (now - state.last > opt.ttl) {
    state.count = 1;
    state.last = now;
  } else {
    if (state.count >= opt.limit)
      throw new HttpException('RateLimitExceeded', HttpStatus.TOO_MANY_REQUESTS);
    state.count += 1;
  }
  const RateLimit = `limit=${opt.limit}, remaining=${opt.limit - state.count}, reset=${state.last + opt.ttl - Date.now()}`;
  c.header('RateLimit', RateLimit);
};
