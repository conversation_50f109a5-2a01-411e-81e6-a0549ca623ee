import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { HDBSalesListingModel } from './hdb-sales.model';

@Auth()
export class HDBSalesController {
  /**
   * Get all HDB sales listings for a specific user
   * @param user_id The user ID to filter listings
   * @returns Array of sales listings for the user
   */
  @Compress()
  async getSalesListingsByUserId({ user_id }: { user_id: number }) {
    console.log("USER ID FOR SALES LISTING",user_id);
    const listings = await HDBSalesListingModel.find({ user_id }).lean();
    return { data: listings };
  }
  @Compress()
  async getAllSalesListings({ user_id }: { user_id: number }) {
   const listings = await HDBSalesListingModel.find({ user_id: { $ne: user_id } }).lean();
   return { data: listings };
  }

  @Compress()
 async getSalesListingById({ _id }: { _id: string }) {
  const listing = await HDBSalesListingModel.findById(_id);
  return { data: listing };
 }
}
