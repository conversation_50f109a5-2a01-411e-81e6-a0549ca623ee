import { Schema, model, type Types } from 'mongoose';

/**
 * Interface for individual chat messages
 */
export interface IChatMessage {
  _id?: Types.ObjectId;
  message: string;
  role: 'buyer' | 'seller' | 'system';
  user_id: number;
  timestamp: Date;
  messageType: 'text' | 'image' | 'video' | 'file';
  edited?: boolean;
  editedAt?: Date;
}

/**
 * Interface for the chat conversation between buyer and seller
 */
export interface IHDBSalesChat {
  _id?: Types.ObjectId;
  listingId: string; // Reference to HDBSalesListing _id
  sellerId: number;
  buyerId: number;
  offerId?: string; // Optional reference to an offer if one exists
  messages: IChatMessage[];
  createdAt?: Date;
  updatedAt?: Date;
}

const ChatMessageSchema = new Schema<IChatMessage>({
  message: { type: String, required: true },
  role: { type: String, enum: ['buyer', 'seller', 'system'], required: true },
  user_id: { type: Number, required: true },
  timestamp: { type: Date, default: Date.now },
  messageType: { type: String, enum: ['text', 'image', 'video', 'file'],
  edited: { type: Boolean, default: false },
  editedAt: { type: Date }
  }
});

const HDBSalesChatSchema = new Schema<IHDBSalesChat>(
  {
    listingId: { type: String, required: true, ref: 'HDBSalesListing' },
    sellerId: { type: Number, required: true },
    buyerId: { type: Number, required: true },
    offerId: { type: String, ref: 'HDBSalesOffer' },
    messages: [ChatMessageSchema]
  },
  { timestamps: true, collection: 'HDBSalesChat' }
);

// Create compound index for faster lookups by listing and participants
HDBSalesChatSchema.index({ listingId: 1, sellerId: 1, buyerId: 1 }, { unique: true });

export const HDBSalesChatModel = model<IHDBSalesChat>('HDBSalesChat', HDBSalesChatSchema);
