import { Schema, model, type Types } from 'mongoose';

export interface IHDBSalesListing {
  _id?: Types.ObjectId;
  blockNumber: string;
  town?: string;
  price?: number;
  psf?: number;
  street?: string;
  salesCondition?: string[];
  listingId: number; // unique 5-digit integer
  createdAt?: Date;
  user_id: number;
}

const HDBSalesListingSchema = new Schema<IHDBSalesListing>(
  {
    blockNumber: { type: String, required: true },
    town: { type: String },
    price: { type: Number },
    psf: { type: Number },
    street: { type: String },
    salesCondition: [{ type: String }],
    listingId: { type: Number, unique: true, required: true },
    createdAt: { type: Date, default: Date.now },
    user_id: { type: Number, required: true },
  },
  { timestamps: true,collection: 'HDBSalesListing' }

);

export const HDBSalesListingModel = model<IHDBSalesListing>('HDBSalesListing', HDBSalesListingSchema);