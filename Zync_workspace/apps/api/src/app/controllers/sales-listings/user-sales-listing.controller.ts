import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { UserSalesListingModel, type IUserSalesListing } from './user-sales-listing.model';

@Auth()
export class UserSalesListingController {
  @Compress()
  async createSalesListing({ body }: { body: IUserSalesListing }) {
    return { data: await UserSalesListingModel.create(body) };
  }
}
