import { type Model, Schema, type Types, model } from 'mongoose';

export interface ICondo {
  _id?: Types.ObjectId;
  url?: string;
  projectName?: string;
  address?: string;
  district?: number;
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
  projectType?: Types.ObjectId;
  developer?: string;
  tenure?: string;
  psf?: number[];
  yearOfCompletion?: number;
  numberOfFloors?: number;
  totalUnits?: number;
  buildings?: {
    address?: string;
    floors?: number;
    units?: number;
  }[];
  facilities?: string[];
  floorPlans?: string[];
  nearestMRT?: Types.ObjectId[];
  nearestSchools?: Types.ObjectId[];
  photos?: string[];
}

// Define the schema for buildings
const CondoSchema = new Schema<ICondo>({
  url: { type: String, default: null },
  projectName: { type: String, default: null },
  address: { type: String, default: null },
  district: { type: Number, default: null },

  // GeoJSON for spatial queries
  location: {
    type: { type: String, enum: ['Point'], default: 'Point' },
    coordinates: { type: [Number], default: null },
  },

  projectType: { type: Schema.Types.ObjectId, ref: 'ProjectType', default: null },
  developer: { type: String, default: null },
  tenure: { type: String, default: null },
  psf: [{ type: Number, default: null }],
  yearOfCompletion: { type: Number, default: null },
  numberOfFloors: { type: Number, default: null },
  totalUnits: { type: Number, default: null },
  buildings: [
    {
      address: { type: String, default: null },
      floors: { type: Number, default: null },
      units: { type: Number, default: null },
    },
  ],
  facilities: [{ type: String }],
  floorPlans: [{ type: String }],
  nearestMRT: [{ type: Schema.Types.ObjectId, ref: 'MetroStation', default: null }],
  nearestSchools: [{ type: Schema.Types.ObjectId, ref: 'School', default: null }],
  photos: [{ type: String }],
});

CondoSchema.index({ location: '2dsphere' });

export const CondoData: Model<ICondo> = model<ICondo>('CondoBuilding', CondoSchema);
