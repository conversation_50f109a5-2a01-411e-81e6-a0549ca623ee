import { type Types } from 'mongoose';
import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { CondoTransactionDump, type ICondoTransactionDump } from './condo-transaction-test.model';
import { CondoData, type ICondo } from './condo.model';

export type CondoByLocationWithDistance = Pick<
  ICondo,
  'projectName' | 'address' | 'totalUnits' | 'location'
> & { distance: number };

@Auth()
export class CondoController {
  /**
   * Get all Condo data
   * @returns All Condo data with total count
   */
  @Compress()
  async getAll({ page, limit }: { page?: number; limit?: number }) {
    const condoData = await CondoData.find()
      .skip((page || 0) * (limit || 50))
      .limit(limit || 50)
      .select('projectName yearOfCompletion address projectType totalUnits location')
      .lean<ICondo[]>();
    return { data: condoData, total: await CondoData.countDocuments() };
  }

  /**
   * Get Condo data by ID
   * @param params Request parameters containing Condo ID
   * @returns Condo data for the specified ID
   */
  @Compress()
  async getById(params: { id: string | Types.ObjectId }) {
    const condoData = await CondoData.findById(params.id)
      .select(
        'projectName yearOfCompletion address projectType totalUnits location tenure floorPlans address district',
      )
      .lean<ICondo>();
    return { data: condoData };
  }

  /**
   * Find within 2km radius of a location
   * @param body Request body containing latitude and longitude and optional minDistance, maxDistance and limit
   * @returns within 2km radius
   */
  @Compress()
  async findByLocation({
    latitude,
    longitude,
    minDistance,
    maxDistance,
    limit,
  }: {
    latitude: number;
    longitude: number;
    minDistance?: number;
    maxDistance?: number;
    limit?: number;
  }) {
    minDistance ||= 0;
    maxDistance ||= 2000; // 2km in meters;
    limit ||= 50;
    const condos: CondoByLocationWithDistance[] = await CondoData.aggregate([
      {
        $geoNear: {
          near: { type: 'Point', coordinates: [longitude, latitude] },
          distanceField: 'distance',
          minDistance,
          maxDistance,
          spherical: true,
        },
      },
      { $project: { projectName: 1, address: 1, totalUnits: 1, location: 1, distance: 1 } },
      { $limit: limit },
    ]);
    return { success: true, center: { latitude, longitude }, condos };
  }

  /**
   * Get location coordinates for Condo transactions
   * @param body Request body containing array of objects with project_name and address
   * @returns Array of location coordinates
   */
  @Compress()
  async getCondoCoordinates(body: {
    transactions: { projectName: string; address: string; district: string }[];
  }) {
    try {
      const { transactions } = body;
      const locationResults = [];

      for (const transaction of transactions) {
        try {
          const searchVal = transaction.projectName + ',' + transaction.address;
          const response = await fetch(
            `https://www.onemap.gov.sg/api/common/elastic/search?searchVal=${encodeURIComponent(searchVal)}&returnGeom=Y&getAddrDetails=Y`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
              },
            },
          );

          const data = await response.json();

          if (data.results && data.results.length > 0) {
            const result = data.results[0];
            locationResults.push({
              projectName: transaction.projectName,
              address: transaction.address,
              latitude: Number(result.LATITUDE),
              longitude: Number(result.LONGITUDE),
              full_address: result.ADDRESS,
            });
          } else {
            console.log(
              `No location data found for ${transaction.projectName} at ${transaction.address}`,
            );
          }
        } catch (error) {
          console.error(
            `Error fetching location for ${transaction.projectName} at ${transaction.address}:`,
            error,
          );
        }
      }
      return { success: true, locations: locationResults };
    } catch (error) {
      console.error('Error in getCondoCoordinates:', error);
      return { success: false, error: 'Failed to fetch location coordinates' };
    }
  }

  /**
   * Get latest 10 transactions by latitude and longitude
   * @param body Request body containing latitude and longitude
   * @returns Latest 10 transactions for the given location
   */
  @Compress()
  async getLatestTransactionsByLocation(body: { lat: string; long: string }) {
    try {
      const { lat, long } = body;
      console.log('Received coordinates:', { lat, long });
      if (!lat || !long) {
        return { success: false, message: 'Latitude and Longitude are required.' };
      }

      const latitude = Number(lat);
      const longitude = Number(long);

      if (isNaN(latitude) || isNaN(longitude)) {
        return { success: false, message: 'Invalid latitude or longitude values.' };
      }

      const documentCount = await CondoTransactionDump.countDocuments();
      console.log('Total documents in CondoTransactionDump collection:', documentCount);

      const transactions: ICondoTransactionDump[] = await CondoTransactionDump.find({
        location: {
          type: 'Point',
          coordinates: [longitude, latitude],
        },
      })
        .sort({ contract_date: -1 }) // Sorting by contract_date (string "YYYY-MM")
        .select('contract_date floor_range area_sqm price district project street property_type')
        .limit(10)
        .lean();

      if (transactions.length === 0) {
        return { success: false, message: 'No transactions found for the provided coordinates.' };
      }

      return {
        success: true,
        count: transactions.length,
        data: transactions,
      };
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return { success: false, message: error.message || 'Internal Server Error' };
    }
  }

  /**
   * Find within 2km radius of a location
   * @param body Request body containing latitude and longitude and optional minDistance, maxDistance and limit
   * @returns within 2km radius
   */
  @Compress()
  async findCondosNearUserLocation({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    const condos: CondoByLocationWithDistance[] = await CondoData.aggregate([
      {
        $geoNear: {
          near: { type: 'Point', coordinates: [longitude, latitude] },
          distanceField: 'distance',
          maxDistance: radius,
        },
      },
      { $project: { projectName: 1, address: 1, totalUnits: 1, location: 1, distance: 1 } },
    ]);
    return { success: true, center: { latitude, longitude }, condos };
  }
}
