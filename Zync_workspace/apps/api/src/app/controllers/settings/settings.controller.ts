import { Dr<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../common/drizzle-helper';
import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { settingsTable } from './settings.entity';

@Auth()
export class SettingsController {
  @Compress()
  async getAll() {
    return { data: await DrizzleHelper.findAll(settingsTable) };
  }

  @Compress()
  async getOne(setting: { key: string }) {
    return { data: await DrizzleHelper.findOne(settingsTable, { where: { key: setting.key } }) };
  }

  async create(setting: { key: string; value: string }) {
    await DrizzleHelper.create(settingsTable, setting);
    return { success: true };
  }

  async update(query: { key: string }, body: { value: string }) {
    const [res] = await Dr<PERSON>zleHelper.update(
      settingsTable,
      { key: query.key },
      { value: body.value },
    );
    if (res.affectedRows === 0)
      await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.create(settingsTable, { key: query.key, value: body.value });
    return { success: true };
  }

  async remove({ key }: { key: string }) {
    await <PERSON><PERSON>zleHelper.destroy(settingsTable, { key });
    return { success: true };
  }
}
