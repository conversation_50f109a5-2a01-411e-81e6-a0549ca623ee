import { wait } from '@lib/common/fun';
import makeFetchCookie from 'fetch-cookie';

const handleTooManyRequests = async (fun: () => Promise<Response>) => {
  while (true) {
    const res = await fun();
    if (res.status !== 429) return res;
    else await wait(60_000);
  }
};

export const checkEthnicQuota = async (data: {
  enquiryBy: string;
  blockId: string;
  street: string;
  ethnicGrp: string;
  citizenship: string;
}) => {
  const fetchWithCookies: typeof fetch = makeFetchCookie(fetch);
  const formPage = await handleTooManyRequests(() =>
    fetchWithCookies('https://services2.hdb.gov.sg/webapp/BB29ETHN/BB29STREET'),
  ).then((res) => res.text());
  const csrfToken = formPage.match(
    /name="(?<name>[A-Fa-f0-9]{6})"\s*value="(?<value>[0-9a-f-]+)"/i,
  )?.groups;
  if (!csrfToken) return {};
  const formData = new URLSearchParams([
    ['actionName', ''],
    ['flgBlockNo', '1'],
    ['flgGetStr', ''],
    ['flgBlkNo', '1'],
    [csrfToken.name, ''],
    ['enquiryBy', data.enquiryBy],
    ['txt_pc', 'PC_'],
    ['blockId', `${data.blockId}`],
    ['blockNoId', ''],
    ['blockTo', data.enquiryBy === 'BYR' ? data.blockId : ''],
    ['street', data.street],
    ['postalCde', ''],
    ['ethnicGrp', `${data.ethnicGrp}`],
    ['citizenship', `${data.citizenship}`],
    [csrfToken.name, csrfToken.value],
  ]);
  const resultHtml = await handleTooManyRequests(() =>
    fetchWithCookies('https://services2.hdb.gov.sg/webapp/BB29ETHN/BB29ETHNIC_ENQ', {
      method: 'POST',
      headers: { 'content-type': 'application/x-www-form-urlencoded' },
      body: formData,
    }),
  ).then((res) => res.text());
  if (data.enquiryBy === 'SLR') {
    console.log("SELLER ETHNIC DATA",resultHtml);
    const infoParagraphs = resultHtml
      .matchAll(/<p style="margin-bottom: 0px;">(.*)<\/p>/g)
      .toArray();
    return { summary: infoParagraphs[0]?.[1].trim(), details: infoParagraphs[1]?.[1].trim() };
  } else if (data.enquiryBy === 'BYR') {
    return { eligibilityInfo: resultHtml.match(/<td width="90%">(.*)<\/td>/)?.[1].trim() };
  }
};
