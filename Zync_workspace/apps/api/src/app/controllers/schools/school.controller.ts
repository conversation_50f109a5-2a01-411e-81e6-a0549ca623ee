import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { type IKinderGartenSchool, KinderGartenSchoolModel } from './kindergarten-school.model';
import { type ISchool, SchoolModel } from './school.model';

@Auth()
export class SchoolController {
  /**
   * Get all Kindergarten School data
   * @returns All Kindergarten School data
   */
  @Compress()
  async getAll() {
    const kindergartenSchoolData = await KinderGartenSchoolModel.find()
      .select('schoolName location')
      .lean<IKinderGartenSchool[]>();

    return { data: kindergartenSchoolData };
  }

  @Compress()
  async findSchoolsWithinRadius({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    const schools = await SchoolModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: radius,
        },
      },
    })
      .select(
        'schoolName address postal location telephoneNumber email town schoolLevel schoolType',
      )
      .lean<ISchool[]>();
    return {
      success: true,
      center: { latitude, longitude },
      schools,
      count: schools.length,
    };
  }
}
