import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';
import { HawkerCentreModel, type IHawkerCentre } from './hawker-center.model';

@Auth()
export class HawkerController {
  /**
   * Find hawker centers within 1km and 2km radius of a property
   * @param body Request body containing latitude and longitude
   * @returns Hawker centers within 1km and 2km radius
   */
  @Compress()
  async findByLocation({ latitude, longitude }: { latitude: number; longitude: number }) {
    // Find hawker centers within 1km radius
    const hawkerCentersWithin1km = await HawkerCentreModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: 1000,
        },
      },
    })
      .limit(50)
      .select(
        'name description buildingName blockHouseNumber postalCode streetName location cookedFoodStalls',
      )
      .lean<IHawkerCentre[]>();

    // Find hawker centers within 2km radius (excluding those already in 1km)
    const hawkerCentersWithin2km = await HawkerCentreModel.find({
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [longitude, latitude],
          },
          $minDistance: 1000,
          $maxDistance: 2000,
        },
      },
    })
      .limit(50)
      .select(
        'name description buildingName blockHouseNumber postalCode streetName location cookedFoodStalls',
      )
      .lean<IHawkerCentre[]>();

    return {
      success: true,
      center: { latitude, longitude },
      hawkerCenters: { withinOneKm: hawkerCentersWithin1km, withinTwoKm: hawkerCentersWithin2km },
      counts: { oneKm: hawkerCentersWithin1km.length, twoKm: hawkerCentersWithin2km.length },
    };
  }

  @Compress()
  async findHawkerCentersWithinRadius({
    latitude,
    longitude,
    radius,
  }: {
    latitude: number;
    longitude: number;
    radius: number;
  }) {
    console.log('Hawker Centres Radius', radius);
    const hawkerCenters = await HawkerCentreModel.find({
      location: {
        $near: {
          $geometry: { type: 'Point', coordinates: [longitude, latitude] },
          $maxDistance: radius,
        },
      },
    })
      .select(
        'name description location buildingName blockHouseNumber postalCode streetName cookedFoodStalls photoUrl',
      )
      .lean<IHawkerCentre[]>();
    return {
      success: true,
      center: { latitude, longitude },
      hawkerCenters,
      count: hawkerCenters.length,
    };
  }
}
