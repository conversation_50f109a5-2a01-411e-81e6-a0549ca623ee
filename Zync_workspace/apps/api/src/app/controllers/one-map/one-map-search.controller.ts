import { Auth } from '../../guards/auth.guard';
import { Compress } from '../../receptor/compression.receptor';

@Auth()
export class OneMapSearchController {
  @Compress()
  async getSearchResults({ searchVal }: { searchVal: string }) {
    try {
      try {
        const response = await fetch(
          `https://www.onemap.gov.sg/api/common/elastic/search?searchVal=${encodeURIComponent(searchVal)}&returnGeom=Y&getAddrDetails=Y`,
          {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
          },
        );

        const data = await response.json();
        return { success: true, locations: data.results };
      } catch (error) {
        console.error(`Error fetching Data for ${searchVal}:`, error);
        return { success: false, error: 'Failed to fetch location coordinates' };
      }
    } catch (error) {
      console.error('Error in getCondoCoordinates:', error);
      return { success: false, error: 'Failed to fetch location coordinates' };
    }
  }
}
