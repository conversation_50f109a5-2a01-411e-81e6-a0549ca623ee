import {
  CopyObjectCommand,
  type CopyObjectCommandInput,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  ListObjectsV2Command,
  PutObjectCommand,
  S3Client,
  paginateListObjectsV2,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { type AwsCredentialIdentity } from '@aws-sdk/types';
import type S3 from 'aws-sdk/clients/s3';
import { and, eq, inArray, sql } from 'drizzle-orm';
import { environment } from '../../environments/environment';
import { drizzleDb } from '../common/db';
import { DrizzleHelper } from '../common/drizzle-helper';
import { type TableMeta } from '../common/tables.const';
import { type FileTableSchema, filesTable } from '../controllers/auth/files.entity';

const getFileExtension = (fname: string) => fname.slice(((fname.lastIndexOf('.') - 1) >>> 0) + 2);

export interface APIFileMeta {
  name: string;
  size: number;
  type: string;
}

export interface IAppFile {
  tableId: number;
  itemId: number;
  file: APIFileMeta;
  desc?: string;
  buffer?: Buffer;
  meta?: any;
}

export interface IHasPic {
  id: number;
  pic?: number;
}

export interface ISingleFile {
  id: number;
  col: string;
  fileId: number;
}

const genUniqKey = (suffix: number) => {
  const ft = new Date().getTime();
  const s = Math.floor(ft / 1000);
  const a = new Uint8Array(new Uint32Array([s]).buffer);
  const b = new Uint8Array([
    Math.floor(Math.random() * 256),
    Math.floor(Math.random() * 256),
    Math.floor(Math.random() * 256),
    Math.floor(Math.random() * 256),
  ]);
  const c = new Uint8Array(new Uint16Array([suffix]).buffer);
  return Buffer.from([...a, ...b, ...c]).toString('hex');
};

export interface IFileParam {
  app?: string;
  tableId?: number;
  itemId?: number;
  path?: string;
}

export interface IFileData {
  desc: string;
  id: number;
  name: string;
  path: string;
  size: number;
  type: string;
  updatedAt: Date;
  url: string;
  meta: any;
}

export class FileMgrService {
  private bucketName: string;
  private fileSystemConfig = {
    s3: {
      driver: 's3',
      key: environment.AWS.ACCESS_KEY_ID,
      secret: environment.AWS.SECRET_ACCESS_KEY,
      region: environment.AWS.DEFAULT_REGION,
      bucket: environment.AWS.BUCKET,
      mainFolder: environment.AWS.MAIN_FOLDER,
      view_url: environment.AWS.VIEW_URL,
      // 'url' : environment.AWS.URL,
      endpoint: environment.AWS.URL,
      use_path_style_endpoint: true,
    },
  };
  private s3v2: S3;
  private s3v3: S3Client;
  private mainFolder: string;

  constructor() {
    const mainBucket = this.getAppMainBucket();
    this.s3v3 = mainBucket.S3v3;
    this.s3v2 = mainBucket.S3v2;
    this.bucketName = mainBucket.bucketName;
    this.mainFolder = mainBucket.mainFolder;
  }

  getAppMainBucket() {
    const fileConfig = this.fileSystemConfig;
    const { region, endpoint } = fileConfig.s3;
    const credentials: AwsCredentialIdentity = {
      accessKeyId: fileConfig.s3.key,
      secretAccessKey: fileConfig.s3.secret,
    };
    const S3v3 = new S3Client({
      credentials,
      region,
      endpoint,
      forcePathStyle: fileConfig.s3.use_path_style_endpoint,
    });

    const S3v2: S3 = environment.AWS.S3V2
      ? new environment.AWS.S3V2({
          credentials,
          region,
          endpoint,
          s3ForcePathStyle: fileConfig.s3.use_path_style_endpoint,
          signatureVersion: 'v4',
        })
      : {};
    const bucketName = fileConfig.s3.bucket;
    const mainFolder = fileConfig.s3.mainFolder;
    return { S3v3, S3v2, bucketName, mainFolder };
  }

  public getBucketName() {
    return this.bucketName;
  }

  public hasFiles(table: TableMeta) {
    return {
      getPresignedUrl: (itemId: number, file: APIFileMeta, desc?: string) =>
        this.getItemsPresignedUrl(table, itemId, file, desc),
      removeFiles: (itemId: number, ids?: number[]) =>
        this.removeItemsFiles({ tableId: table.id, itemId }, ids),
      getFileUrl: (itemId: number, fileId: number) =>
        this.getItemsFileUrl(table.id, itemId, fileId),
      setFile: (item: ISingleFile, file: APIFileMeta, type?: string) =>
        this.setItemsFile(table, item, file, type),
      deleteFile: (itemId: number, fileId: number) => this.deleteSingle(table, itemId, fileId),
      getFiles: (itemId: number, opt: { fileUrlRequire?: boolean; excludeFileId?: number } = {}) =>
        this.getItemsFiles({ tableId: table.id, itemId }, opt),
    };
  }

  public hasThumbnail(table: TableMeta) {
    return {
      setThumbnail: (item: IHasPic, file: APIFileMeta, type?: string) =>
        this.setItemsThumbnail(table, item, file, type),
      getThumbnailUrl: (item: IHasPic) => this.getThumbnailUrl(table.id, item),
      deleteThumbnail: (item: IHasPic) => this.deleteThumbnail(table, item),
    };
  }

  public uploadFiles(files: IAppFile[]) {
    const res = files.map(async (file, index) => {
      const ext = getFileExtension(file.file.name) ?? '';
      const vName = genUniqKey(index) + (ext ? `.${ext}` : '');
      const s3File = await this.s3v3.send(
        new PutObjectCommand({
          Bucket: this.bucketName,
          Key: this.genFileKey({
            app: this.mainFolder,
            tableId: file.tableId,
            itemId: file.itemId,
            path: vName,
          }),
          CacheControl: 'public, max-age=31536000, immutable',
          Body: file.buffer,
          // ACL: 'public_read',
        }),
      );

      const newFile = {} as FileTableSchema;
      newFile.tableId = file.tableId;
      newFile.itemId = file.itemId;
      newFile.name = file.file.name;
      newFile.path = vName;
      newFile.size = file.file.size;
      newFile.meta = file.meta;
      newFile.type = file.file.type;
      const dbFile = await DrizzleHelper.create(filesTable, newFile);
      return { s3File, dbFile: dbFile.insertId };
    });
    return Promise.all(res);
  }

  public findOne(id: number, tableId: number): Promise<FileTableSchema> {
    return DrizzleHelper.findOne(filesTable, { where: { itemId: id, tableId } });
  }

  public deleteFile(files: FileTableSchema[]) {
    const res = files.map(async (file) => {
      const cmd = {
        Bucket: this.bucketName,
        Key: this.genFileKey({
          app: this.mainFolder,
          tableId: file.tableId,
          itemId: file.itemId,
          path: file.path,
        }),
      };
      const [s3File, dbFile] = await Promise.all([
        environment.AWS.S3V2
          ? this.s3v2.deleteObject(cmd).promise()
          : this.s3v3.send(new DeleteObjectCommand(cmd)),
        DrizzleHelper.destroy(filesTable, { id: file.id }),
      ]);
      return { s3File, dbFile };
    });
    return Promise.all(res);
  }

  public async deleteFiles(files: FileTableSchema[]) {
    if (files?.length === 0) return;
    const Objects = files.map((file) => {
      return {
        Key: this.genFileKey({
          app: this.mainFolder,
          tableId: file.tableId,
          itemId: file.itemId,
          path: file.path,
        }),
      };
    });

    const cmd = { Bucket: this.bucketName, Delete: { Objects, Quiet: true } };
    const [s3File, dbFile] = await Promise.all([
      environment.AWS.S3V2
        ? this.s3v2.deleteObjects(cmd).promise()
        : this.s3v3.send(
            new DeleteObjectsCommand({ Bucket: this.bucketName, Delete: { Objects } }),
          ),
      drizzleDb.delete(filesTable).where(
        inArray(
          filesTable.id,
          files.map((v) => v.id),
        ),
      ),
    ]);
    return { s3File, dbFile };
  }

  public deleteFilesFromS3(paths: string[]) {
    const Objects = paths.map((v) => ({ Key: v }));
    const s3File = this.s3v3.send(
      new DeleteObjectsCommand({ Bucket: this.bucketName, Delete: { Objects } }),
    );
    return s3File;
  }

  private async emptyS3Directory(bucket: string, dir: string) {
    const listParams = { Bucket: bucket, Prefix: dir };
    let done = false;
    while (!done) {
      const listedObjects = await this.s3v3.send(new ListObjectsV2Command(listParams));
      if (listedObjects.Contents.length === 0) return;
      const deleteParams = {
        Bucket: bucket,
        Delete: { Objects: listedObjects.Contents.map(({ Key }) => ({ Key })) },
      };
      await (environment.AWS.S3V2
        ? this.s3v2.deleteObjects(deleteParams).promise()
        : this.s3v3.send(new DeleteObjectsCommand(deleteParams)));
      done = !listedObjects.IsTruncated;
    }
  }

  public deleteDir(
    dirs: any[],
    options: { keyGen?: (data: { app: any; vName: string }) => string } = {},
  ) {
    const Objects = dirs.map((dir) => {
      const Key = options.keyGen
        ? options.keyGen({ app: this.mainFolder, vName: dir.path })
        : this.genFileKey({
            app: this.mainFolder,
            tableId: dir.tableId,
            itemId: dir.itemId,
            path: dir.path,
          });
      return { Key };
    });
    return Promise.all(Objects.map((v) => this.emptyS3Directory(this.bucketName, v.Key)));
  }

  public genFileKey({ app = this.mainFolder, tableId, itemId, path }: IFileParam) {
    return `${app ? `${app}/` : ''}${tableId}/${itemId}/${path}`;
  }

  public genFileUrl(
    data: IFileParam,
    options: { keyGen?: (data: { app: any; vName: string }) => string } = {},
  ) {
    return `${this.fileSystemConfig.s3.view_url}${this.bucketName}/${
      options.keyGen
        ? options.keyGen({ app: this.mainFolder, vName: data.path })
        : this.genFileKey(data)
    }`;
  }

  public async getPreURL(
    file: IAppFile,
    expire = 60,
    options: { keyGen?: (data: { app: any; vName: string }) => string } = {},
  ) {
    const ext = getFileExtension(file.file.name) ?? '';
    const vName = genUniqKey(0) + (ext ? `.${ext}` : '');
    const Key = options.keyGen
      ? options.keyGen({ app: this.mainFolder, vName })
      : this.genFileKey({
          app: this.mainFolder,
          tableId: file.tableId,
          itemId: file.itemId,
          path: vName,
        });
    let preUrl: string;
    if (environment.AWS.S3V2) {
      preUrl = await this.s3v2.getSignedUrlPromise('putObject', {
        Bucket: this.bucketName,
        Key,
        Expires: expire,
        // CacheControl: 'public, max-age=31536000, immutable',
        // ACL: 'public_read',
      });
    } else {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key,
        CacheControl: 'public, max-age=31536000, immutable',
      });
      preUrl = await getSignedUrl(this.s3v3, command, { expiresIn: expire });
    }
    const newFile = {} as FileTableSchema;
    newFile.tableId = file.tableId;
    newFile.itemId = file.itemId;
    newFile.name = file.file.name;
    newFile.path = vName;
    newFile.size = file.file.size;
    newFile.meta = file.meta;
    newFile.type = file.file.type;
    const dbFile = await DrizzleHelper.create(filesTable, newFile);
    return { preUrl, dbFile: { id: dbFile.insertId } };
  }

  public copyFileInS3(params: CopyObjectCommandInput) {
    return this.s3v3.send(new CopyObjectCommand(params));
  }

  async getAllFilesFromS3(path: string) {
    const opts = {
      Bucket: this.bucketName /* required */,
      // ContinuationToken: 'STRING_VALUE',
      // Delimiter: 'STRING_VALUE',
      // EncodingType: url,
      // FetchOwner: true || false,
      // MaxKeys: 'NUMBER_VALUE',
      Prefix: path,
      // RequestPayer: requester,
      // StartAfter: 'STRING_VALUE'
    };

    const files = [];
    for await (const data of paginateListObjectsV2({ client: this.s3v3 }, opts))
      files.push(data.Contents);
    return files.flat();
  }

  public async getItemsFiles(
    query: { tableId: number; itemId: number },
    { excludeFileId, fileUrlRequire }: { fileUrlRequire?: boolean; excludeFileId?: number },
  ): Promise<IFileData[]> {
    const files = await DrizzleHelper.findAll(filesTable, { where: query });
    if (!files?.length) return [];
    const documents = files
      .filter((v) => v.id !== excludeFileId)
      .map((v) => {
        const path = v.path;
        const imageUrl = fileUrlRequire
          ? this.genFileUrl({ tableId: query.tableId, itemId: query.itemId, path })
          : undefined;
        return {
          desc: v.desc,
          id: v.id,
          name: v.name,
          path: v.path,
          size: v.size,
          type: v.type,
          updatedAt: v.updatedAt,
          url: imageUrl,
          meta: v.meta,
        };
      });
    return documents;
  }

  private getThumbnailUrl(tableId: number, item: IHasPic) {
    return this.getItemsFileUrl(tableId, item.id, item.pic);
  }

  private async getItemsFileUrl(tableId: number, itemId: number, fileId: number) {
    if (!fileId) return;
    const thumb = await DrizzleHelper.findOne(filesTable, {
      where: { id: fileId, tableId, itemId },
      select: ['path'],
    });
    if (!thumb) return;
    return this.genFileUrl({ tableId, itemId, path: thumb.path });
  }

  private async removeItemsFiles(query: { tableId: number; itemId: number }, ids?: number[]) {
    const files = await drizzleDb
      .select()
      .from(filesTable)
      .where(
        and(
          eq(filesTable.tableId, query.tableId),
          eq(filesTable.itemId, query.itemId),
          ids && inArray(filesTable.id, ids),
        ),
      );
    await this.deleteFiles(files);
    return true;
  }

  private async getItemsPresignedUrl(
    table: TableMeta,
    itemId: number,
    file: APIFileMeta,
    desc?: string,
  ) {
    const thumb: IAppFile = {
      tableId: table.id,
      itemId,
      file,
      buffer: null,
      meta: { desc: desc || `${table.name}_files` },
    };
    const preUrlData = await this.getPreURL(thumb);
    return { preUrl: preUrlData.preUrl, id: preUrlData.dbFile.id };
  }

  private async deleteSingle(table: TableMeta, itemId: number, fileId: number) {
    if (!fileId) return;
    const file = await DrizzleHelper.findOne(filesTable, {
      where: { id: fileId, tableId: table.id, itemId },
    });
    if (file) await this.deleteFiles([file]);
  }

  private deleteThumbnail(table: TableMeta, item: IHasPic) {
    return this.deleteSingle(table, item.id, item.pic);
  }

  public async deleteBulkByIds(files: number[]) {
    const fileWithData = await drizzleDb
      .select()
      .from(filesTable)
      .where(inArray(filesTable.id, files));
    if (fileWithData?.length) await this.deleteFiles(fileWithData);
  }

  async setItemsFile(table: TableMeta, item: ISingleFile, file: APIFileMeta, desc?: string) {
    const deleteP = this.deleteSingle(table, item.id, item.fileId);
    const thumb: IAppFile = {
      tableId: table.id,
      itemId: item.id,
      file,
      buffer: null,
      meta: { desc: desc || `${table.name}_pic` },
    };
    const data = await this.getPreURL(thumb);
    await drizzleDb.execute(
      sql.raw(`UPDATE ${table.name} SET ${item.col} = ${data.dbFile.id} WHERE id = ${item.id}`),
    );
    data.preUrl = data.preUrl.replace(environment.AWS.URL, environment.AWS.VIEW_URL);
    await deleteP;
    return { preUrl: data.preUrl, id: data.dbFile.id };
  }

  async setItemsThumbnail(table: TableMeta, item: IHasPic, file: APIFileMeta, type?: string) {
    const { preUrl, id: pic } = await this.setItemsFile(
      table,
      { id: item.id, fileId: item.pic, col: 'pic' },
      file,
      type,
    );
    return { preUrl, pic };
  }
}
