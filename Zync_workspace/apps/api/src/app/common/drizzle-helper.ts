import { and, eq } from 'drizzle-orm';
import { drizzleDb } from './db';

export type TableType = {
  $inferSelect: any;
} & Record<string, any>;

const generalWhere = (table: TableType, where: Record<string, any>) => {
  const conditions = Object.entries(where).map(([key, value]) => eq(table[key], value));
  return and(...conditions);
};

const generalSelect = <T extends TableType, S extends keyof T['$inferSelect']>(
  table: T,
  select: S[],
): { [K in S]: T[K] } => {
  const sel = {} as { [K in S]: T[K] };
  for (const key of select) sel[key] = table[key];
  return sel;
};

const findOne = async <T extends TableType, S extends keyof T['$inferSelect']>(
  table: T,
  options: { where?: Partial<T['$inferSelect']>; select?: S[] },
): Promise<T['$inferSelect']> => {
  let query: any = drizzleDb
    .select(options.select ? generalSelect(table, options.select) : undefined)
    .from(table as any);
  if (options.where) query = query.where(generalWhere(table, options.where));
  const [res] = await query.limit(1);
  return res;
};

const findAll = <T extends TableType, S extends keyof T['$inferSelect']>(
  table: T,
  options?: { where?: Partial<T['$inferSelect']>; select?: S[] },
): Promise<T['$inferSelect'][]> => {
  let query: any = drizzleDb
    .select(options?.select ? generalSelect(table, options.select) : undefined)
    .from(table as any);
  if (options?.where) query = query.where(generalWhere(table, options.where));
  return query;
};

const create = <T extends TableType>(table: T, data: Partial<T['$inferSelect']>) => {
  return drizzleDb
    .insert(table as any)
    .values(data)
    .then((res) => res[0]);
};

const update = <T extends TableType>(
  table: T,
  where: Partial<T['$inferSelect']>,
  data: Partial<T['$inferSelect']>,
) => {
  return drizzleDb
    .update(table as any)
    .set(data)
    .where(generalWhere(table, where));
};

const destroy = <T extends TableType>(table: T, whereClause: Partial<T['$inferSelect']>) => {
  return drizzleDb.delete(table as any).where(generalWhere(table, whereClause));
};

export const isExist = async <T extends TableType>(
  table: T,
  key: keyof T['$inferSelect'],
  val: any,
  attributes: (keyof T['$inferSelect'])[] = [],
): Promise<T['$inferSelect'] | null> => {
  const result = await findOne(table, {
    where: { [key]: val } as any,
    select: [key, ...attributes],
  });
  return result || null;
};

export const DrizzleHelper = { findOne, findAll, create, update, destroy, isExist };
