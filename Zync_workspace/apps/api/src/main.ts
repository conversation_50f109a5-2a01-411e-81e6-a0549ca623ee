import { serve } from '@hono/node-server';
import { RootServiceTester } from '@lib/common/test-root-service';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { env } from 'node:process';
import { startApplication, useGlobalGuards } from './api.server';
import { connectMongo } from './app/common/db';
import { RateLimit } from './app/guards/rate-limit.guard';
import { getS3V2 } from './environments/env-modules';
import { environment } from './environments/environment';

const bootstrap = async () => {
  env.TZ = environment.appRun.TZ || 'UTC';
  if ((environment as any).AWS.S3V2) (environment as any).AWS.S3V2 = getS3V2();
  RootServiceTester.check = !environment.production;
  await connectMongo();
  const app = new Hono();
  const globalPrefix = '/api';
  if (!environment.production) app.use(`${globalPrefix}/*`, cors());
  if (environment.production) {
    for (const method of ['error', 'warn', 'log', 'info', 'debug', 'trace']) {
      console[method] = () => {
        //
      };
    }
  }

  useGlobalGuards(RateLimit({ limit: 100, ttl: 15 * 60 * 1000 })); // 100 in 15min

  const apiRouter = startApplication();
  app.route(globalPrefix, apiRouter);
  if (environment.production) {
    const { createServer } = await import('node:http2');
    serve({ fetch: app.fetch, port: environment.appRun.port, createServer }, (info) => {
      console.log(`Server is running: http://${info.address}:${info.port}`);
    });
  } else {
    serve({ fetch: app.fetch, port: environment.appRun.port }, (info) => {
      console.log(`Server is running: http://${info.address}:${info.port}`);
    });
  }
};

bootstrap();
