import { HttpException } from '@lib/common/common.error';
import { makeKebab, wait } from '@lib/common/fun';
import { Hono } from 'hono';
// import { streamSSE } from 'hono/streaming';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
// import { Observable } from 'rxjs';
import { CONTROLLERS } from './app/controllers';
import type { GuardHandler } from './app/guards/guards';
import { Guards, resolveMeta } from './app/guards/guards';
import { ContextManager } from './app/hooks/ctx.hooks';
import { Receptors, processReceptors } from './app/receptor/receptors';
import { environment } from './environments/environment';

export const useGlobalGuards = (guard: any) => guard(globalThis);

export const startApplication = () => {
  const apiRouter = new Hono();

  const processQuery = (query: Record<string, string>) => {
    const finalQuery: Record<string, any> = {};
    for (const [key, value] of Object.entries(query)) {
      if (value.startsWith('$')) finalQuery[key] = value.slice(1);
      else finalQuery[key] = JSON.parse(value);
    }
    return finalQuery;
  };
  const globalGuards = Guards.get(globalThis) ?? new Set<GuardHandler>();
  for (const [controllerName, _class] of Object.entries(
    CONTROLLERS as Record<string, new () => any>,
  )) {
    if (!environment.production) {
      console.log(`\n----- ${environment.chalk.blue.underline.bold(controllerName)} -----`);
    }
    const router = new Hono();
    const controller = new _class();
    const controllerRoute = `/${makeKebab(controllerName.replace(/Controller$/, ''))}`;
    const type = (name: string) => (name.startsWith('get') ? 0 : 1);
    const methodsName = Object.getOwnPropertyNames(Object.getPrototypeOf(controller)).sort(
      (a, b) => {
        if (type(a) < type(b)) return -1;
        if (type(a) > type(b)) return 1;
        return a.localeCompare(b);
      },
    );
    const classGuards = Guards.get(_class) ?? new Set<GuardHandler>();
    for (const method of methodsName) {
      const fn = controller[method];
      if (!(typeof fn === 'function' && method !== 'constructor')) continue;
      const route = `/${makeKebab(method)}`;
      const type = method.startsWith('get') ? 'get' : 'post';
      const handlerGuards = Guards.get(fn) ?? new Set<GuardHandler>();
      router[type](route, async (c) => {
        const query = () => processQuery(c.req.query());
        const call = (...args: any[]): Promise<any> /* | Observable<any> */ => {
          ContextManager.ctx = c;
          const res = fn.call(controller, ...args);
          ContextManager.ctx = undefined;
          return res;
        };
        try {
          const getMeta = resolveMeta([fn, _class, globalThis]);
          for (const guard of globalGuards.difference(classGuards).difference(handlerGuards))
            await guard({ c, target: globalThis, handler: fn, getMeta });
          for (const guard of classGuards.difference(handlerGuards))
            await guard({ c, target: _class, handler: fn, getMeta });
          for (const guard of handlerGuards) await guard({ c, target: fn, handler: fn, getMeta });
          const rawResponse =
            fn.length === 0
              ? call()
              : type === 'get'
                ? call(query())
                : fn.length === 1
                  ? call(await c.req.json())
                  : call(query(), await c.req.json());
          // if (rawResponse instanceof Observable) {
          //   return streamSSE(c, async (stream) => {
          //     let id = 0;
          //     let abort = false;
          //     const controller = new AbortController();
          //     stream.onAbort(() => {
          //       abort = true;
          //       controller.abort();
          //     });
          //     try {
          //       for await (const data of otag(rawResponse, controller.signal)) {
          //         if (abort) break;
          //         stream.writeSSE({
          //           data: JSON.stringify(data),
          //           event: 'message',
          //           id: String(id++),
          //         });
          //       }
          //     } catch {
          //       //
          //     }
          //   });
          // }
          const response = await processReceptors(c, Receptors.get(fn) ?? [], await rawResponse);
          if (response instanceof Response) return response;
          if (!environment.production) await wait(300);
          return c.json(response);
        } catch (error) {
          if (!environment.production) await wait(300);
          if (error instanceof HttpException)
            return c.json(error.error, error.status as ContentfulStatusCode);
          return c.json({ error: error.message, stack: error.stack }, 500);
        }
      });
      if (!environment.production) {
        const { chalk } = environment;
        console.log(
          `${chalk[type === 'get' ? 'yellow' : 'cyan'](type.toUpperCase().padEnd(5, ' '))} ${chalk.green(`${controllerRoute}${route}`)}`,
        );
      }
    }
    apiRouter.route(controllerRoute, router);
  }

  return apiRouter;
};
