{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/apps/api", "format": ["cjs"], "bundle": false, "main": "apps/api/src/main.ts", "tsConfig": "apps/api/tsconfig.app.json", "assets": ["apps/api/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api:build"}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}}}