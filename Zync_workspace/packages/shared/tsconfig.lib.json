{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../dist/out-tsc", "declaration": true, "types": ["node"], "lib": ["ESNext", "DOM"], "target": "ESNext", "strict": false, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts", "../hono/src/app/controllers/auth/paginator.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}