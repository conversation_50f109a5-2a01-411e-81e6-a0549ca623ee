import { EventEmitter } from 'eventemitter3';
import { AbortErrorException, firstEvent } from '../common/fun';

export class AudioBufferPlayer extends EventEmitter {
  #audioCtx!: AudioContext;
  #gainNode!: GainNode;
  #panner!: StereoPannerNode;
  #audioBuffer!: AudioBuffer;
  #source?: AudioBufferSourceNode;
  #ctxOffset = 0;
  #lastVolume = 0;
  #offset = 0;
  #playing = false;
  #started = false;
  #timeUpdateInterval?: number;
  #currentReq: { ended: boolean; aborted: boolean } | undefined;

  #timeUpdate = () => this.emit('timeupdate');

  #ended = () => {
    this.#reset();
    this.#currentReq!.ended = true;
    this.emit('ended');
  };

  #setup() {
    if (!this.#audioCtx) this.#audioCtx = (window as any).audioContext ?? new AudioContext();
    if (!this.#gainNode) this.#gainNode = new GainNode(this.#audioCtx);
    if (!this.#panner) this.#panner = new StereoPannerNode(this.#audioCtx, { pan: 0 });
  }

  #setupSource() {
    if (this.#source) this.#clearSource();
    this.#source = new AudioBufferSourceNode(this.#audioCtx, { buffer: this.#audioBuffer });
    this.#source.connect(this.#gainNode).connect(this.#panner).connect(this.#audioCtx.destination);
    this.#addEndedListener();
  }

  #stopTimer() {
    if (!this.#timeUpdateInterval) return;
    clearInterval(this.#timeUpdateInterval);
    this.#timeUpdateInterval = undefined;
  }

  #startTimer() {
    this.#stopTimer();
    if (this.listenerCount('timeupdate'))
      this.#timeUpdateInterval = window.setInterval(this.#timeUpdate, 100);
  }

  #addEndedListener() {
    this.#source?.addEventListener('ended', this.#ended, false);
  }

  #removeEndedListener() {
    this.#source?.removeEventListener('ended', this.#ended, false);
  }

  #reset() {
    if (!this.#started) return;
    this.#ctxOffset = this.#audioCtx.currentTime;
    this.#offset = 0;
    this.#playing = false;
    this.#started = false;
    this.#clearSource();
    this.#stopTimer();
  }

  #clearSource() {
    this.#audioCtx.suspend();
    this.#source!.stop();
    this.#source!.disconnect();
    this.#gainNode.disconnect();
    this.#panner.disconnect();
    this.#removeEndedListener();
    this.#source = undefined;
  }

  ready() {
    if (!this.#audioCtx) this.#setup();
  }

  async load(arrayBuffer: ArrayBuffer) {
    if (!this.#audioCtx) this.#setup();
    this.loadAudioBuffer(await this.#audioCtx.decodeAudioData(arrayBuffer));
  }

  loadAudioBuffer(audioBuffer: AudioBuffer) {
    if (!this.#audioCtx) this.#setup();
    if (this.#currentReq) {
      if (!this.#currentReq.ended) {
        this.#currentReq.aborted = true;
        this.emit('aborted');
      }
      this.#reset();
      this.#currentReq = undefined;
    }
    this.#audioBuffer = audioBuffer;
    this.#currentReq = { ended: false, aborted: false };
  }

  get duration() {
    return this.#audioBuffer.duration;
  }

  get currentTime() {
    return this.#offset + this.#audioCtx.currentTime - this.#ctxOffset;
  }

  get volume() {
    return this.#gainNode.gain.value;
  }

  get pan() {
    return this.#panner.pan.value;
  }

  get isPlaying() {
    return this.#playing;
  }

  get isMuted() {
    return this.#lastVolume !== 0;
  }

  setSeeker(time: number) {
    this.#ctxOffset = this.#audioCtx.currentTime;
    this.#offset = time;
    if (this.#started) {
      this.#started = false;
      this.#setupSource();
      if (this.#playing) {
        if (this.#audioCtx.state === 'suspended') this.#audioCtx.resume();
        this.#source?.start(0, this.#offset);
        this.#started = true;
      }
    }
  }

  setVolume(volume: number) {
    if (this.#lastVolume !== 0) this.unMute();
    this.#gainNode.gain.value = volume;
  }

  mute() {
    if (this.#lastVolume !== 0) return;
    this.#lastVolume = this.#gainNode.gain.value;
    this.#gainNode.gain.value = 0;
  }

  unMute() {
    if (this.#lastVolume === 0) return;
    this.#gainNode.gain.value = this.#lastVolume;
    this.#lastVolume = 0;
  }

  setPan(pan: number) {
    this.#panner.pan.value = pan;
  }

  resetPan() {
    this.#panner.pan.value = 0;
  }

  resume() {
    if (this.#started && !this.#playing) {
      this.#audioCtx.resume();
      this.#playing = true;
      this.#startTimer();
      return true;
    }
    return false;
  }

  pause() {
    if (this.#started && this.#playing) {
      this.#audioCtx.suspend();
      this.#playing = false;
      this.#stopTimer();
      return true;
    }
    return false;
  }

  play() {
    if (this.#started && this.#playing) return false;
    this.#playing = true;
    this.#startTimer();
    if (this.#audioCtx.state === 'suspended') this.#audioCtx.resume();
    if (this.#started) {
      this.#audioCtx.resume();
    } else {
      this.#started = true;
      if (!this.#source) this.#setupSource();
      this.#source!.start(0, this.#offset);
    }
    return true;
  }

  stop() {
    if (this.#currentReq) {
      if (!this.#currentReq.ended) {
        this.#currentReq.aborted = true;
        this.emit('aborted');
      }
    }
    this.#reset();
  }

  destroy() {
    this.#reset();
    this.#audioBuffer = undefined!;
    this.#gainNode = undefined!;
    this.#panner = undefined!;
    this.#audioCtx.close();
    this.#audioCtx = undefined!;
    this.#ctxOffset = 0;
    this.#lastVolume = 0;
  }
}

export const SilverPlayer = new AudioBufferPlayer();

const play = async (signal?: AbortSignal) => {
  SilverPlayer.play();
  // SilverPlayer.setVolume(1.0);
  const res = await firstEvent([
    [signal!, ['abort']],
    [SilverPlayer, ['ended', 'aborted']],
  ]);
  if (res.emitter === signal) {
    SilverPlayer.stop();
    throw new AbortErrorException();
  }
  if (res.name === 'aborted') {
    throw new AbortErrorException();
  }
};

export const convertPCMToAudioBuffer = (
  arrayBuffer: ArrayBuffer | Int16Array,
  sampleRate = 24000,
) => {
  // 16-bit, 24KHz, 1 channel (mono) PCM
  const audioContext = (window as any).audioContext ?? new AudioContext();
  const int16Array = arrayBuffer instanceof Int16Array ? arrayBuffer : new Int16Array(arrayBuffer);
  const float32Array = new Float32Array(int16Array.length);
  // Normalize 16-bit PCM data to the range -1.0 to 1.0
  for (let i = 0; i < int16Array.length; i++) float32Array[i] = int16Array[i] / 0x8000;
  const audioBuffer = audioContext.createBuffer(1, float32Array.length, sampleRate);
  audioBuffer.getChannelData(0).set(float32Array);
  return audioBuffer;
};

export const convertPCMToWAV = (
  arrayBuffer: ArrayBuffer | Int16Array,
  sampleRate = 24000,
  channels = 1,
) => {
  // Ensure PCM data is in Int16Array
  const int16Array = arrayBuffer instanceof Int16Array ? arrayBuffer : new Int16Array(arrayBuffer);

  // Calculate necessary sizes
  const byteRate = sampleRate * channels * 2; // 16-bit = 2 bytes
  const blockAlign = channels * 2;
  const wavHeaderSize = 44;
  const wavBuffer = new ArrayBuffer(wavHeaderSize + int16Array.byteLength);
  const view = new DataView(wavBuffer);

  // Write the WAV header
  let offset = 0;

  // RIFF chunk descriptor
  writeString(view, offset, 'RIFF');
  offset += 4;
  view.setUint32(offset, 36 + int16Array.byteLength, true); // ChunkSize
  offset += 4;
  writeString(view, offset, 'WAVE');
  offset += 4;

  // fmt sub-chunk
  writeString(view, offset, 'fmt ');
  offset += 4;
  view.setUint32(offset, 16, true); // Subchunk1Size (16 for PCM)
  offset += 4;
  view.setUint16(offset, 1, true); // AudioFormat (1 for PCM)
  offset += 2;
  view.setUint16(offset, channels, true); // NumChannels
  offset += 2;
  view.setUint32(offset, sampleRate, true); // SampleRate
  offset += 4;
  view.setUint32(offset, byteRate, true); // ByteRate
  offset += 4;
  view.setUint16(offset, blockAlign, true); // BlockAlign
  offset += 2;
  view.setUint16(offset, 16, true); // BitsPerSample
  offset += 2;

  // data sub-chunk
  writeString(view, offset, 'data');
  offset += 4;
  view.setUint32(offset, int16Array.byteLength, true); // Subchunk2Size
  offset += 4;

  // Write PCM data
  const pcmData = new Uint8Array(wavBuffer, wavHeaderSize);
  pcmData.set(new Uint8Array(int16Array.buffer));

  // Return WAV data as a Blob
  return new Blob([wavBuffer], { type: 'audio/wav' });
};

const writeString = (view: DataView, offset: number, string: string) => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

export const playAudioBufferPCM = async (arrayBuffer: ArrayBuffer, signal?: AbortSignal) => {
  if (signal?.aborted) throw new AbortErrorException();
  SilverPlayer.loadAudioBuffer(convertPCMToAudioBuffer(arrayBuffer));
  if (signal?.aborted) throw new AbortErrorException();
  await play(signal);
};

export const playAudioBuffer2 = async (audioBuffer: AudioBuffer, signal?: AbortSignal) => {
  if (signal?.aborted) throw new AbortErrorException();
  SilverPlayer.loadAudioBuffer(audioBuffer);
  if (signal?.aborted) throw new AbortErrorException();
  await play(signal);
};

export const playAudioBuffer = async (arrayBuffer: ArrayBuffer, signal?: AbortSignal) => {
  if (signal?.aborted) throw new AbortErrorException();
  await SilverPlayer.load(arrayBuffer);
  if (signal?.aborted) throw new AbortErrorException();
  await play(signal);
};
