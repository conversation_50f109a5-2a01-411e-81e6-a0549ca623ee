import htm from 'htm';

const emptyTags = [
  'area',
  'base',
  'br',
  'col',
  'command',
  'embed',
  'hr',
  'img',
  'input',
  'keygen',
  'link',
  'meta',
  'param',
  'source',
  'track',
  'wbr',
];

const map: Record<string, string> = { '&': 'amp', '<': 'lt', '>': 'gt', '"': 'quot', "'": 'apos' };
const esc = (str: string) => String(str).replace(/[&<>"']/g, (s) => `&${map[s]};`);
const setInnerHTMLAttr = 'dangerouslySetInnerHTML';
const DOMAttributeNames: Record<string, string> = { className: 'class', htmlFor: 'for' };
const sanitized: Record<string, boolean> = {};

/** Hyper script reviver that constructs a sanitized HTML string. */
export const vHtml = (
  type: string | ((props: Record<string, any>) => string),
  props: Record<string, any>,
  ...children: any[]
) => {
  let s = '';
  props ||= {};
  if (typeof type === 'function') {
    props['children'] = children;
    return type(props);
  }
  if (type) {
    s += `<${type}`;
    if (props)
      for (const key in props) {
        if (
          key !== setInnerHTMLAttr &&
          props[key] !== false &&
          props[key] !== null &&
          props[key] !== undefined
        )
          s += ` ${DOMAttributeNames[key] ?? esc(key)}="${esc(props[key])}"`;
      }
    s += '>';
  }
  if (!emptyTags.includes(type)) {
    if (props[setInnerHTMLAttr]) s += props[setInnerHTMLAttr].__html;
    else {
      for (const child of children.flat(1000)) {
        if (!child) continue;
        s += sanitized[child] === true ? child : esc(child);
      }
    }
    s += type ? `</${type}>` : '';
  }
  sanitized[s] = true;
  return s;
};
export const h = vHtml;
export const html = htm.bind(vHtml);
