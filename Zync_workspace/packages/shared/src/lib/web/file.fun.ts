import { wait } from '../common/fun';

export const getFileMeta = (file: File) => {
  return { name: file.name, size: file.size, type: file.type };
};

export const dataURLToFile = async (v: string, name: string, type?: string) => {
  const data = await fetch(v);
  const blob = await data.blob();
  return new File([blob], name, { type: type ?? blob.type });
};

export const base64ToFile = async (data: string, fileName: string, options?: FilePropertyBag) => {
  const blob = await fetch(`data:image/png;base64,${data}`).then((v) => v.blob());
  return new File([blob], fileName, options);
};

export const dataURLtoBlob = (dataURL: string) => fetch(dataURL).then((res) => res.blob());

export const dataURLtoBlobSync = (dataURL: string) => {
  const [type, data] = dataURL.split(',');
  const mime = type.match(/:(.*?);/)?.[1];
  const bstr = atob(data);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) u8arr[n] = bstr.charCodeAt(n);
  return new Blob([u8arr], { type: mime } as BlobPropertyBag);
};

export const blobToDataURL = (blob: Blob | File): Promise<string> => {
  return new Promise((res, rej) => {
    const reader = new FileReader();
    reader.onload = () => res(reader.result as string);
    reader.onerror = (e) => rej(e);
    reader.readAsDataURL(blob);
  });
};

export const blobToBase64 = blobToDataURL;

export const blobToFile = (blob: Blob, fileName: string): File => {
  const b = blob as any;
  b.lastModifiedDate = new Date();
  b.name = fileName;
  return b as File;
};

export const fileToUint8Array = async (file: File): Promise<Uint8Array> =>
  new Uint8Array(await file.arrayBuffer());

export const uint8ArrayToBlob = (file: Uint8Array, options?: BlobPropertyBag) =>
  new Blob([file], options);

export const jsonToFile = (data: any, filename: string) =>
  new File([JSON.stringify(data)], filename, { type: 'application/json' });

export const hashFile = async (blob: File, algorithm: AlgorithmIdentifier = 'SHA-256') => {
  const hashBuffer = await crypto.subtle.digest(algorithm, await blob.arrayBuffer());
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
};

export const arrayBufferOrBlobToFile = (
  bfrOrBlb: ArrayBuffer | Blob,
  fileName: string,
  mimeType?: string,
): File => {
  if (!(bfrOrBlb instanceof Blob)) bfrOrBlb = new Blob([bfrOrBlb], { type: mimeType });
  const file = new File([bfrOrBlb], fileName, { type: mimeType });
  return file;
};

export const downloadFile = async (file: File | Blob | string, fileName?: string) => {
  const url = typeof file === 'string' ? file : URL.createObjectURL(file);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName ?? (file instanceof File ? file.name : 'file');
  a.click();
  a.remove();
  await wait(1000);
  if (typeof file !== 'string') URL.revokeObjectURL(url);
};

export const uint8ArrayToBase64 = (uint8Array: Uint8Array) => {
  let binaryString = '';
  const length = uint8Array.length;
  const { fromCharCode } = String;
  for (let i = 0; i < length; i++) binaryString += fromCharCode(uint8Array[i]);
  return btoa(binaryString);
};

export const base64ToUint8Array = (base64: string) => {
  const binaryString = atob(base64);
  const length = binaryString.length;
  const bytes = new Uint8Array(length);
  for (let i = 0; i < length; i++) bytes[i] = binaryString.charCodeAt(i);
  return bytes;
};

export const textToGZipBase64 = async (text: string) => {
  const unit8array = new Uint8Array(
    await new Response(
      new Blob([text], { type: 'text/plain' }).stream().pipeThrough(new CompressionStream('gzip')),
    ).arrayBuffer(),
  );
  return uint8ArrayToBase64(unit8array);
};

export const gZipBase64ToText = async (base64: string) => {
  return await new Response(
    new Blob([base64ToUint8Array(base64)], { type: 'application/gzip' })
      .stream()
      .pipeThrough(new DecompressionStream('gzip')),
  ).text();
};

export const getVideoDurationByURL = async (url: string) => {
  const video = document.createElement('video');
  video.setAttribute('crossorigin', 'anonymous');
  video.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');
  video.src = url;
  const d = await new Promise<number>((resolve) => {
    video.onloadedmetadata = () => resolve(video.duration);
  });
  video.src = '';
  video.remove();
  return d;
};

export const stringToFile = (str: string, fileName: string, type = 'text/plain') =>
  new File([str], fileName, { type });

export const concatUint8Arrays = (arr1: Uint8Array, arr2: Uint8Array) => {
  const totalLength = arr1.length + arr2.length;
  const result = new Uint8Array(totalLength);
  result.set(arr1, 0);
  result.set(arr2, arr1.length);
  return result;
};
