import { Injectable } from '@angular/core';
import { loadScript } from '@lib/common/script-loader';
import { createStore, get, set } from 'idb-keyval';

const sqliteDbStore = createStore('sqlite-db-store', 'sqlite-db-store');
@Injectable({ providedIn: 'root' })
export class SQLWebService {
  package: any;
  SQL: any;
  dbs: Record<string, any> = {};

  init() {
    this.package ??= this.loadPackage();
    return this.package;
  }

  async loadPackage() {
    await loadScript('https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.12.0/sql-wasm.js');
    this.SQL = await (window as any).initSqlJs({
      locateFile: (filename: string) =>
        `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.12.0/${filename}`,
    });
  }

  public async loadDB(path: string) {
    await this.init();
    this.dbs[path] ??= this._loadDB(path);
    return this.dbs[path];
  }

  private async _loadDB(path: string) {
    let buffer = await get(path, sqliteDbStore);
    if (!buffer) {
      buffer = new Uint8Array(await (await fetch(path)).arrayBuffer());
      set(path, buffer, sqliteDbStore);
    }
    return new this.SQL.Database(buffer);
  }

  public saveDb(path: string, db: any) {
    set(path, new Uint8Array(db.export()), sqliteDbStore);
  }
}
