import { ROLES } from '../../../common/const/roles';
import { hasAny } from '../../../common/fun';

export interface Role {
  id: number;
  name: string;
}

export class ActiveUser {
  public readonly isAdmin: boolean;
  public readonly isSuperAdmin: boolean;
  public readonly isMaster: boolean;
  public readonly createdAt: Date;
  public readonly email: string;
  public readonly pic: string;
  public readonly id: number;
  public readonly name: string;
  public readonly roleIds: number[];
  public readonly roles: Role[];
  public readonly updatedAt: Date;
  public readonly username: string;
  public readonly folderId: number;
  public readonly isActive: boolean;
  public readonly meta: { TFARequire: boolean };
  public readonly permissions: any;

  constructor(data: any = {}) {
    this.id = data.id;
    this.name = data.name;
    this.email = data.email;
    this.username = data.username;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.isActive = data.isActive;
    this.meta = data.meta;
    this.pic = data.pic;
    this.roles = data.roles;
    this.folderId = data.folderId;
    this.roleIds = this.roles.map((v) => v.id);
    this.isAdmin = hasAny(this.roleIds, [ROLES.SUPER_ADMIN.id, ROLES.MASTER.id]);
    this.isMaster = hasAny(this.roleIds, [ROLES.MASTER.id]);
    this.isSuperAdmin = hasAny(this.roleIds, [ROLES.SUPER_ADMIN.id]);
    // this.permissions = buildPermission((x: number[]) => this.hasAnyRole(x));
  }

  public hasRole(id: number) {
    return this.roleIds.includes(id);
  }

  public hasAnyRole(ids: number[]) {
    return hasAny(this.roleIds, ids);
  }
}
