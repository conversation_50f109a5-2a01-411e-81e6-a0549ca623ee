import type { <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  Component,
  Injectable,
  ViewEncapsulation,
  inject,
  viewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../../common/test-root-service';
import { QrScannerService } from './qr-scanner.service';

interface QRScannerModalResult {
  action: boolean;
  value?: string;
}

@Injectable({ providedIn: 'root' })
export class QRScannerModalService {
  readonly #matDialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  open(): Promise<QRScannerModalResult> {
    const ref = this.#matDialog.open<QRScannerModalComponent, any, void>(QRScannerModalComponent, {
      disableClose: true,
      width: '100%',
      height: '100%',
      maxWidth: '100%',
      maxHeight: '100%',
      panelClass: 'full-screen-transparent-dialog',
    });
    return firstValueFrom(ref.afterClosed() as any);
  }
}

@Component({
  selector: 'app-qr-scanner-modal',
  imports: [MatDialogModule, MatButtonModule, MatIconModule],
  template: `<div class="flex flex-col">
    <button class="btn btn-circle btn-outline ml-auto mt-5 mr-5" (click)="close()">
      <mat-icon>close</mat-icon>
    </button>
  </div> `,
  encapsulation: ViewEncapsulation.None,
  styles: [
    `
      .full-screen-transparent-dialog .mat-mdc-dialog-surface {
        background-color: transparent;
        box-shadow: none;
      }
    `,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QRScannerModalComponent implements OnInit, OnDestroy {
  readonly #qrScannerService = inject(QrScannerService);

  readonly ref = viewChild.required<ElementRef>('ref');
  readonly #dialogRef: MatDialogRef<QRScannerModalComponent> = inject(MatDialogRef);

  async ngOnInit() {
    const text = await this.#qrScannerService.scan();
    if (text) this.submit(text);
    else this.#dialogRef.close({ action: false });
  }

  public close() {
    this.#dialogRef.close({ action: false });
  }

  public submit(text: string) {
    this.#dialogRef.close({ action: true, value: text });
  }

  public ngOnDestroy(): void {
    this.#qrScannerService.destroy();
  }
}
