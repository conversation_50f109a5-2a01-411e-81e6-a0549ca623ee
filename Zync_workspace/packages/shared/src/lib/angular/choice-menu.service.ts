import { DIALOG_DATA, Dialog, DialogModule, DialogRef } from '@angular/cdk/dialog';
import type { ElementRef, OnInit } from '@angular/core';
import { Component, Injectable, NgZone, inject, viewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { daisyMerge } from '@lib/common/tailwind-merge';
import { delay, firstValueFrom, race } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';
import { dialogFromCenter } from './dialog-align';
import { SVGIconComponent } from './svg-icon.component';

export interface ActionI {
  name: string;
  icon?: string;
  url?: string;
  key?: string;
  action?: (...x: any[]) => any;
  cssClass?: string;
  iconClass?: string;
  titleClass?: string;
}

// export type ActionsT = ActionI[];

interface ActionDataI {
  actions: ActionI[];
  heading?: string;
  innerClass?: string;
  inlineIcon?: boolean;
  commonCssClass?: string;
  commonIconClass?: string;
  commonTitleClass?: string;
  headDomClass?: string;
  headDom?: (root: HTMLElement, onClose: Promise<void>) => void;
}

// interface FormattedActionI {
//   name: string;
//   icon?: string;
//   url?: string;
//   key: string;
//   cssClass?: string;
//   iconClass?: string;
//   titleClass?: string;
// }

interface ActionResultI {
  action: boolean;
  value?: ActionI;
}

@Injectable({ providedIn: 'root' })
export class ChoiceMenuService {
  readonly #dialog = inject(Dialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: ActionDataI, options: any = {}): Promise<ActionResultI | undefined> {
    const onSelected = Promise.withResolvers<ActionResultI | undefined>();
    const dialogRef = this.#dialog.open(ChoiceMenuComponent, {
      panelClass: daisyMerge(
        'bg-base-100 rounded-box !max-w-[calc(100%-50px)]',
        (options.panelClass || dialogFromCenter.panelClass).join(' '),
      ).split(' '),
      data: { data, onSelected },
    });
    return firstValueFrom(race(onSelected.promise, dialogRef.closed.pipe(delay(10)))) as Promise<
      ActionResultI | undefined
    >;
  }
}

@Component({
  imports: [MatIconModule, SVGIconComponent, DialogModule],
  selector: 'app-choice-menu',
  template: `<div class="divide-y divide-slate-700 flex flex-col max-h-[90vh]">
    <div
      [class]="
        daisyMerge(
          'pt-4 px-4 pb-2',
          heading || data.headDom ? 'block' : 'hidden',
          data.headDom ? data.headDomClass : 'text-2xl font-medium text-center'
        )
      "
      #headBox
    >
      {{ heading }}
    </div>

    <div
      [class]="
        daisyMerge(
          'grid grid-cols-2 md:grid-cols-3 gap-4 p-4 overflow-auto px-4 pb-4',
          data.innerClass || ''
        )
      "
    >
      @for (item of actions; track $index) {
        <button
          (click)="select(item)"
          [class]="
            daisyMerge(
              'flex flex-col items-center p-2 gap-2 text-center rounded-lg hover:bg-base-200 outline-none focus:bg-base-200',
              data.inlineIcon ? 'flex-row' : '',
              data.commonCssClass || '',
              item.cssClass || ''
            )
          "
        >
          @if (item.icon) {
            <mat-icon [class]="daisyMerge(data.commonIconClass || '', item.iconClass || '')">{{
              item.icon
            }}</mat-icon>
          } @else if (item.url) {
            <app-svg-icon
              [class]="daisyMerge(data.commonIconClass || '', item.iconClass || '')"
              [icon]="item.url"
            />
          }
          <div [class]="daisyMerge('text-sm', data.commonTitleClass || '', item.titleClass || '')">
            {{ item.name }}
          </div>
        </button>
      }
    </div>
  </div>`,
})
export class ChoiceMenuComponent implements OnInit {
  readonly headBox = viewChild.required<ElementRef<HTMLDivElement>>('headBox');
  readonly #dialogRef: DialogRef<ChoiceMenuComponent> = inject(DialogRef<any>);
  readonly #ngZone = inject(NgZone);
  readonly #data: { data: ActionDataI; onSelected: PromiseWithResolvers<ActionResultI> } =
    inject(DIALOG_DATA);

  public actions: ActionI[] = [];
  public heading: string | undefined;

  daisyMerge = daisyMerge;
  public data!: ActionDataI;

  ngOnInit(): void {
    const actionList: ActionI[] = this.actions;
    const data = this.#data.data;
    const actions = data.actions;
    this.heading = data.heading;
    this.data = data;
    for (const action of actions) {
      const key = action.key;
      const act: ActionI = { ...action, key, titleClass: action.titleClass ?? '' };
      // if (typeof action === 'string')
      //   act = { name: action, key, titleClass: data.commonTitleClass ?? '' };

      // [act.name, ...act.lines] = act.name.split('\n');
      actionList.push(act);
    }

    this.handleHeadDom();
  }

  handleHeadDom() {
    if (!this.headBox() || !this.data.headDom) return;
    this.#ngZone.runOutsideAngular(() => {
      this.data.headDom!(
        this.headBox().nativeElement,
        firstValueFrom(this.#dialogRef.closed.pipe(delay(10))) as Promise<void>,
      );
    });
  }

  select(item: ActionI) {
    this.#data.onSelected.resolve({ action: true, value: item });
    this.#dialogRef.close();
  }
}
