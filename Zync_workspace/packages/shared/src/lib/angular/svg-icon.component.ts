import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Component, ElementRef, inject, input } from '@angular/core';

export const PREFETCHED_ICONS = { icons: {} as Record<string, string> };

const library = {
  local: (path: string) => `icons/${path}.svg`,
  iconify: (path: string) => `https://api.iconify.design/${path.replace(':', '/')}.svg`,
};

type SVGOptions = { width?: string; height?: string; svgStyle?: string; svgClass?: string };

class IconProvider {
  cache = new Map<string, Promise<string>>();

  getUrl(url: string) {
    if (!url) return '';
    for (const key in library) {
      if (url.startsWith(`${key}:`)) {
        url = (library as any)[key](url.replace(`${key}:`, ''));
      }
    }
    return url;
  }

  adjustSize(
    svg: string,
    options: SVGOptions = { width: '1em', height: '1em', svgStyle: '', svgClass: '' },
  ) {
    const d = new DOMParser().parseFromString(svg, 'image/svg+xml');
    const svgElement = d.documentElement;
    if (options.width !== 'auto') svgElement.setAttribute('width', options.width!);
    if (options.height !== 'auto') svgElement.setAttribute('height', options.height!);
    if (options.svgStyle !== '') svgElement.setAttribute('style', options.svgStyle!);
    if (options.svgClass !== '') svgElement.setAttribute('class', options.svgClass!);
    svgElement.setAttribute('fill', 'currentColor');
    return svgElement.outerHTML;
  }

  async downloadSvg(iconName: string) {
    const url = this.getUrl(iconName);
    const res = await fetch(url);
    if (!res.ok) {
      console.error(`Failed to fetch svg: ${url}`);
      return;
    }
    return res.text();
  }

  async loadSvg(iconName: string, options?: SVGOptions) {
    if (!iconName) return;
    const svg =
      (PREFETCHED_ICONS.icons[iconName] as any) ??
      this.cache.get(iconName)! ??
      this.downloadSvg(iconName);
    if (!svg) return;
    this.cache.set(iconName, svg);
    return this.adjustSize(await svg, options);
  }
}

export const IconProviderService = new IconProvider();

@Component({
  selector: 'app-svg-icon',
  template: '',
  styles: `
    :host {
      display: inline-block;
      vertical-align: middle;
      line-height: 1;
    }
  `,
})
export class SVGIconComponent implements OnChanges, OnDestroy, OnInit {
  readonly #el = inject(ElementRef);

  readonly icon = input.required<string>();
  readonly width = input('1em');
  readonly height = input('1em');
  readonly svgStyle = input<string>('');
  readonly svgClass = input<string>('');
  readonly observe = input<boolean>(true);

  private observer: IntersectionObserver | null = null;

  public ngOnInit() {
    if (this.observe()) {
      this.setupIntersectionObserver();
    } else {
      const icon = this.icon();
      if (icon) this.injectSvg(icon);
    }
  }

  public ngOnChanges(): void {
    const icon = this.icon();
    if (!icon) return;
    if (IconProviderService.cache.has(icon)) {
      this.injectSvg(icon);
      return;
    }
    if (this.observe()) {
      if (this.observer) {
        this.observer.disconnect();
        this.setupIntersectionObserver();
      } else {
        this.setupIntersectionObserver();
      }
    }
  }

  private setupIntersectionObserver() {
    const hostElement = this.#el.nativeElement;
    this.observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          this.observer?.disconnect();
          const icon = this.icon();
          if (icon) this.injectSvg(icon);
        }
      },
      { root: null, threshold: 0.1 },
    );
    this.observer.observe(hostElement);
  }

  private async injectSvg(icon: string) {
    const hostElement = this.#el.nativeElement;
    const svg = await IconProviderService.loadSvg(icon, {
      width: this.width(),
      height: this.height(),
      svgStyle: this.svgStyle(),
      svgClass: this.svgClass(),
    });
    if (svg) hostElement.innerHTML = svg;
  }

  public ngOnDestroy(): void {
    this.observer?.disconnect();
  }
}
