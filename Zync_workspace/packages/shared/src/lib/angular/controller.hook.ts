import { HttpClient } from '@angular/common/http';
import { DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import type { ObservableInput, ObservedValueOf, OperatorFunction } from 'rxjs';
import { Observable, fromEvent, switchMap, takeUntil } from 'rxjs';
import { firstValue, makeKebab, simplifyPath } from '../common/fun';

export const p2o$ = (fn: (signal: AbortSignal) => Promise<any>) => {
  return new Observable((sub) => {
    const controller = new AbortController();
    fn(controller.signal)
      .then((v) => (sub.next(v), sub.complete()))
      .catch((e) => sub.error(e));
    return () => controller.abort();
  });
};

export const switchMapPromise = <T, R, O extends ObservableInput<R>>(
  fn: (signal: AbortSignal, v: T, i: number) => Promise<R>,
) =>
  switchMap((v: T, i: number) => p2o$((signal) => fn(signal, v, i))) as OperatorFunction<
    T,
    ObservedValueOf<O>
  >;

interface OptionsI {
  httpClient?: HttpClient;
  destroyRef?: DestroyRef;
  signal?: AbortSignal;
}

const paramHandler = (params: any) => {
  if (!params) return {};
  const output: Record<string, string> = {};
  for (const [k, v] of Object.entries(params)) {
    if (v === undefined) return;
    if (typeof v === 'string') output[k] = `$${v}`;
    else output[k] = JSON.stringify(v);
  }
  return output;
};

export const useControllerConfig = {
  baseUrl: '',
  token: (() => {
    try {
      const t = '8dd879ed_bcf2_497b_bdef_ee73a1809c02';
      const x = localStorage.getItem(t)!;
      return JSON.parse(atob(x)).accessToken;
    } catch {
      return '';
    }
  })(),
};

export const useController = <T = any>(
  controllerName = '',
  opt: OptionsI = {},
): T & { with(opt: OptionsI): T } => {
  let { httpClient, destroyRef } = opt;
  const { signal } = opt;
  if (!('httpClient' in opt)) httpClient ??= inject(HttpClient);
  if (!('destroyRef' in opt)) destroyRef ??= inject(DestroyRef);
  const proxy = new Proxy({} as any, {
    get: (target, prop: string) => {
      if (prop === 'with')
        return (_opt: OptionsI) =>
          useController(controllerName, {
            httpClient: _opt.httpClient ?? httpClient,
            destroyRef: _opt.destroyRef ?? destroyRef,
            signal: _opt.signal ?? signal,
          });
      const httpMethod = prop.startsWith('get') ? 'get' : 'post';
      const url =
        useControllerConfig.baseUrl +
        simplifyPath(`/${makeKebab(controllerName)}/${makeKebab(prop)}`);
      const toPromise = (obs: Observable<any>) => {
        if (destroyRef && signal)
          obs = obs.pipe(takeUntilDestroyed(destroyRef), takeUntil(fromEvent(signal, 'abort')));
        else if (destroyRef) obs = obs.pipe(takeUntilDestroyed(destroyRef));
        else if (signal) obs = obs.pipe(takeUntil(fromEvent(signal, 'abort')));
        return firstValue(obs);
      };
      if (httpMethod === 'get')
        return (params: any) => {
          // BatchManagerService.add({
          //   url: `${url.replace(useControllerConfig.baseUrl, '/api')}?${new URLSearchParams(paramHandler(params)).toString()}`,
          //   method: 'GET',
          //   token: useControllerConfig.token,
          // });
          return toPromise(httpClient!.get(url, { params: paramHandler(params) }));
        };

      return (params: any, body: any) => {
        // BatchManagerService.add({
        //   url: `${url.replace(useControllerConfig.baseUrl, '/api')}?${new URLSearchParams(paramHandler(params)).toString()}`,
        //   method: 'POST',
        //   body: body ?? {},
        //   token: useControllerConfig.token,
        // });
        if (body === undefined) [params, body] = [{}, params];
        return toPromise(httpClient!.post(url, body ?? {}, { params: paramHandler(params) }));
      };
    },
  });
  return proxy;
};

export const useControllerTest = <T = any>(
  controllerName = '',
  opt: OptionsI = {},
): T & { with(opt: OptionsI): T } => {
  const { signal } = opt;
  const proxy = new Proxy({} as any, {
    get: (target, prop: string) => {
      if (prop === 'with')
        return (_opt: OptionsI) => useController(controllerName, { signal: _opt.signal ?? signal });
      const httpMethod = prop.startsWith('get') ? 'get' : 'post';
      const url =
        useControllerConfig.baseUrl +
        simplifyPath(`/${makeKebab(controllerName)}/${makeKebab(prop)}`);
      if (httpMethod === 'get') {
        return (params: any) => {
          const sp = new URLSearchParams(paramHandler(params));
          return fetch(`${url}?${sp.toString()}`, {
            signal,
            headers: { Authorization: `Bearer ${useControllerConfig.token}` },
          }).then((res) => res.json());
        };
      }

      return (params: any, body: any) => {
        if (body === undefined) [params, body] = [{}, params];
        const sp = new URLSearchParams(paramHandler(params));
        return fetch(`${url}?${sp.toString()}`, {
          method: 'POST',
          body: JSON.stringify(body ?? {}),
          signal,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${useControllerConfig.token}`,
          },
        }).then((res) => res.json());
      };
    },
  });
  return proxy;
};

(window as any).useController = useControllerTest;
