import {
  type AfterViewInit,
  Component,
  ElementRef,
  Input,
  type OnChanges,
  type SimpleChanges,
  inject,
} from '@angular/core';

@Component({ selector: 'silver-dom-injector', template: '' })
export class DomInjectorComponent implements AfterViewInit, OnChanges {
  readonly #el = inject(ElementRef);
  @Input() dom!: HTMLElement;

  ngAfterViewInit() {
    this.insertDOM();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dom'] && !changes['dom'].isFirstChange()) this.insertDOM();
  }

  private insertDOM() {
    const el = this.#el.nativeElement;
    if (!el) return;
    if (this.dom) {
      while (el.firstChild) el.removeChild(el.firstChild);
      el.appendChild(this.dom);
    }
  }
}
