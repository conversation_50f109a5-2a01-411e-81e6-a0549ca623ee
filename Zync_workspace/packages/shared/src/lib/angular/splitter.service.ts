import { Injectable, NgZone, inject } from '@angular/core';
import { fromEvent, mergeMap, takeUntil, tap } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';
import { prevent } from './fun';

const mousemove$ = fromEvent<MouseEvent>(document, 'mousemove');
const mouseup$ = fromEvent<MouseEvent>(document, 'mouseup');

export const splitter = (
  parent: HTMLElement,
  left: HTMLElement,
  right: HTMLElement,
  resize: HTMLElement,
  config: any = {},
) => {
  const leftPercent = config.left || 50;
  const rightPercent = 100 - leftPercent;
  left.style.width = `calc(${leftPercent}% - ${resize.offsetWidth / 2}px)`;
  right.style.width = `calc(${rightPercent}% - ${resize.offsetWidth / 2}px)`;
  let offsetLeft = 0;
  return fromEvent<MouseEvent>(resize, 'mousedown').pipe(
    tap(prevent<MouseEvent>),
    tap(() => (offsetLeft = parent.getBoundingClientRect().left)),
    mergeMap(() =>
      mousemove$.pipe(
        tap(prevent<MouseEvent>),
        tap((e) => {
          const totalWidth = parent.offsetWidth;
          const leftWidth = e.clientX - offsetLeft;
          const leftP = (leftWidth / totalWidth) * 100;
          left.style.width = `${leftP}%`;
          right.style.width = `${100 - leftP}%`;
        }),
        takeUntil(mouseup$),
      ),
    ),
  );
};

@Injectable({ providedIn: 'root' })
export class SplitterService {
  readonly #ngZone = inject(NgZone);

  constructor() {
    EnsureSingleInstance(this);
  }

  public splitter = (
    parent: HTMLElement,
    left: HTMLElement,
    right: HTMLElement,
    resize: HTMLElement,
    config: any = {},
  ) =>
    this.#ngZone.runOutsideAngular(() => splitter(parent, left, right, resize, config).subscribe());
}
