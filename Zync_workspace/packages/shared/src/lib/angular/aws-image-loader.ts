import type { ImageLoaderConfig } from '@angular/common';

const prefix = 'https://s3-ap-southeast-1.amazonaws.com//';

const genURL = (src: string, edits: any) => {
  const prop = { bucket: '', key: src.replace(prefix, ''), edits };
  return `https://execute-api..amazonaws.com/prod/?x=/${btoa(JSON.stringify(prop))}`;
};

export const awsImageLoader = (config: ImageLoaderConfig) => {
  if (!config.src?.startsWith(prefix)) return config.src;
  const modification: any = { resize: [{ width: config.width }] };
  if (config.isPlaceholder) modification.grayscale = [];
  return genURL(config.src, modification);
};
