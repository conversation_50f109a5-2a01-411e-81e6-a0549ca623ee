import type { OnInit } from '@angular/core';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  inject,
} from '@angular/core';
import type { FormGroup } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import type { ThemePalette } from '@angular/material/core';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import type { ProgressSpinnerMode } from '@angular/material/progress-spinner';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { firstValueFrom } from 'rxjs';
import { EnsureSingleInstance } from '../common/test-root-service';

export interface ProgressModalRefI {
  setProgress: (value: number) => void;
  dialogRef: MatDialogRef<ProgressComponent>;
  close: () => void;
  onClose: Promise<any>;
}
export interface ModalDataI {
  heading: string;
  btn1Name?: string;
  btn2Name?: string;
  options: {
    determinate?: boolean;
    innerClass?: string;
    color?: string;
    value?: number;
    noAction?: boolean;
    showText?: string;
  };
}

@Injectable({ providedIn: 'root' })
export class ProgressModalService {
  readonly #dialog = inject(MatDialog);

  constructor() {
    EnsureSingleInstance(this);
  }

  public open(data: ModalDataI, options = {}) {
    const onOpen = Promise.withResolvers<ProgressModalRefI>();
    const opt = { width: '400px', data: { data, onOpen }, ...options };
    this.#dialog.open(ProgressComponent, opt);
    return onOpen.promise;
  }
}

@Component({
  imports: [FormsModule, MatButtonModule, MatDialogModule, MatIconModule, MatProgressSpinnerModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `<div mat-dialog-title style="font-size: 1.2rem" class="!px-4 !pb-3 text-center">
      {{ modalData.heading }}
    </div>
    <mat-dialog-content class="mat-typography !p-0 !text-inherit">
      <mat-progress-spinner
        [color]="modalData.options.color ?? 'primary'"
        [mode]="modalData.options.determinate ? 'determinate' : 'indeterminate'"
        [value]="value"
        class="!m-auto !my-6"
      />
      @if (modalData.options.showText) {
        <div class="text-center">
          {{ modalData.options.showText }}
        </div>
      }
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      @if (!modalData.options.noAction) {
        <button mat-button [mat-dialog-close]="false" color="warn">
          {{ btn1Name }}
        </button>
      }
    </mat-dialog-actions> `,
})
export class ProgressComponent implements OnInit {
  readonly #dialogRef: MatDialogRef<ProgressComponent> = inject(MatDialogRef);
  readonly cdRef = inject(ChangeDetectorRef);
  readonly #data: { data: ModalDataI; onOpen: PromiseWithResolvers<ProgressModalRefI> } =
    inject(MAT_DIALOG_DATA);
  public formGroup!: FormGroup<any>;
  public modalData!: ModalDataI;
  public btn1Name!: string;
  public btn2Name!: string;
  public color: ThemePalette = 'primary';
  public mode: ProgressSpinnerMode = 'indeterminate';
  public value = 0;

  public ngOnInit(): void {
    this.modalData = this.#data.data;
    this.value = this.modalData.options.value ?? 0;
    this.btn1Name = this.modalData.btn1Name ?? 'Cancel';
    this.btn2Name = this.modalData.btn2Name ?? 'Confirm';
    this.#data.onOpen.resolve({
      setProgress: this.setProgress.bind(this),
      dialogRef: this.#dialogRef,
      close: () => this.#dialogRef.close(),
      onClose: firstValueFrom(this.#dialogRef.afterClosed()),
    });
  }

  setProgress(value: number) {
    this.value = value;
    this.cdRef.detectChanges();
  }
}
