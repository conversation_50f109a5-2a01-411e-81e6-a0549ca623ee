import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Injectable,
  NgZone,
  inject,
} from '@angular/core';
import type { MatDialogRef } from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import type { Subscription } from 'rxjs';
import { firstValueFrom, fromEvent, take } from 'rxjs';
import { lazyFastQueue, readableByteSize } from '../common/fun';
import { LifeDirective } from './directives/life.directive';

interface DownloadFileIStatusI {
  download?: number;
  total?: number;
  done?: boolean;
  determinate?: boolean;
  working?: boolean;
  progress?: HTMLProgressElement;
  statusElement?: HTMLDivElement;
  icon?: HTMLElement;
  cancel?: () => void;
  controller?: AbortController;
  signalSub?: Subscription;
}

export interface DownloadFileListI {
  displayName?: string;
  url: string;
  signal?: AbortSignal;
  _info?: DownloadFileIStatusI;
}

async function* streamAsyncIterable(stream: ReadableStream<Uint8Array>) {
  const reader = stream.getReader();
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) return;
      yield value;
    }
  } finally {
    reader.releaseLock();
  }
}

const bodyToUint8Array = async (
  body: ReadableStream<Uint8Array>,
  progress?: (loaded: number) => void,
  type?: string,
) => {
  const chunks: Uint8Array[] = [];
  let total = 0;
  for await (const chunk of streamAsyncIterable(body)) {
    chunks.push(chunk);
    total += chunk.length;
    progress?.(total);
  }
  return new Blob(chunks, { type });
};

@Component({
  selector: 'app-file-downloader',
  imports: [MatIconModule, LifeDirective],
  template: `<div class="flex flex-col p-6">
    <h2>Downloading Files</h2>
    <div class="flex flex-col gap-2">
      @for (item of fileDownloaderService._fileList; track item) {
        <div class="p-1 truncate">
          {{ item.displayName || item.url || 'loading...' }}
        </div>
        <div
          #progressElement
          class="flex flex-row items-center gap-2"
          appLife
          (lifeStatus)="register(progressElement, $event, item)"
        >
          <progress class="progress"></progress>
          <div class="w-20 text-right">---</div>
          <mat-icon
            matTooltip="Cancel"
            class="cursor-pointer text-error !overflow-visible"
            (click)="item._info!.cancel?.()"
            >cancel</mat-icon
          >
        </div>
      }
    </div>
  </div>`,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileDownloaderComponent {
  readonly #cdRef = inject(ChangeDetectorRef);
  readonly fileDownloaderService = inject(FileDownloaderService);
  private progressBars = new Map<HTMLDivElement, any>();

  constructor() {
    this.fileDownloaderService.cdRef = this.#cdRef;
  }

  register(div: HTMLDivElement, status: 'OnInit' | 'OnDestroy', item: DownloadFileListI) {
    if (status === 'OnInit') {
      this.progressBars.set(div, item);
      const progressElement = div.querySelector('progress')!;
      const statusElement = div.querySelector('div')!;
      const iconElement = div.querySelector('mat-icon')! as HTMLElement;
      const info = item._info!;
      if (info.determinate) {
        progressElement.max = 1;
        progressElement.value = info.download! / info.total!;
      }
      if (info.working) progressElement.classList.add('progress-accent');
      info.progress = progressElement;
      info.statusElement = statusElement;
      info.icon = iconElement;
    } else if (status === 'OnDestroy') {
      const info = item._info!;
      info.progress = undefined;
      info.statusElement = undefined;
      info.icon = undefined;
      this.progressBars.delete(div);
    }
  }
}

@Injectable({ providedIn: 'root' })
export class FileDownloaderService {
  readonly #dialog = inject(MatDialog);
  readonly #ngZone = inject(NgZone);
  cdRef!: ChangeDetectorRef;
  _fileList: DownloadFileListI[] = [];
  #fileQueue!: (v: DownloadFileListI, opt: { signal?: AbortSignal }) => Promise<Blob>;
  #dialogRef: MatDialogRef<FileDownloaderComponent, any> | undefined;

  constructor() {
    this.#init();
  }

  #init() {
    this.#fileQueue = lazyFastQueue<DownloadFileListI, Blob>(
      2,
      (fileInfo, _, { signal }) => {
        this.#openModal();
        return this.#fileDownload(fileInfo, signal);
      },
      () => {
        this._fileList = [];
        this.#dialogRef?.close();
      },
    );
  }

  #cancel(item: DownloadFileListI) {
    const info = item._info!;
    info.cancel = undefined;
    info.working = false;
    info.progress!.classList.remove('progress-accent');
    info.progress!.classList.add('progress-error');
    info.statusElement!.innerText = 'Canceled';
    info.controller!.abort();
  }

  async #fileDownload(item: DownloadFileListI, signal?: AbortSignal) {
    const info = item._info!;
    info.working = true;
    info.cancel = () => this.#cancel(item);
    {
      const progressBar = info.progress;
      if (progressBar) {
        progressBar.max = 1;
        progressBar.classList.add('progress-accent');
      }
    }
    const response = await fetch(item.url, { signal });
    const total = Number(response.headers.get('content-length'));
    const type = response.headers.get('content-type')!;
    info.total = total;
    info.determinate = !Number.isNaN(total);
    const progress = (loaded: number) => {
      info.download = loaded;
      if (info.progress) {
        info.progress.value = loaded / total;
        info.statusElement!.innerText = readableByteSize(loaded);
      }
    };
    const result = await bodyToUint8Array(response.body!, progress, type!);
    info.signalSub?.unsubscribe();
    info.done = true;
    {
      const progressBar = info.progress!;
      if (progressBar) {
        progressBar.classList.remove('progress-accent');
        progressBar.value = 1;
        progressBar.classList.add('progress-success');
        info.icon!.innerText = 'check_circle';
        info.icon!.classList.add('text-error');
        info.icon!.classList.add('text-success');
      }
    }
    return result;
  }

  async #openModal() {
    if (this.#dialogRef) return;
    this.#dialogRef = this.#dialog.open(FileDownloaderComponent, {
      width: '540px',
      height: '540px',
      disableClose: true,
    });
    await firstValueFrom(this.#dialogRef.afterClosed());
    this.#dialogRef = undefined;
  }

  showPanel() {
    this.#openModal();
  }

  public downloadFiles(list: DownloadFileListI[]) {
    return this.#ngZone.runOutsideAngular(() => {
      return Promise.allSettled(
        list.map((v) => {
          this._fileList.push(v);
          const info = {
            download: 0,
            total: 0,
            done: false,
            determinate: false,
            working: false,
            controller: new AbortController(),
            signalSub: undefined as Subscription | undefined,
            cancel: () => {
              this._fileList.splice(this._fileList.indexOf(v), 1);
              info.controller!.abort();
              info.cancel = undefined as any;
              this.cdRef.detectChanges();
            },
          };
          v._info = info;
          v._info.signalSub = v.signal
            ? fromEvent(v.signal, 'abort')
                .pipe(take(1))
                .subscribe(() => info.cancel?.())
            : undefined;
          return this.#fileQueue(v, { signal: info.controller?.signal });
        }),
      );
    });
  }
}
