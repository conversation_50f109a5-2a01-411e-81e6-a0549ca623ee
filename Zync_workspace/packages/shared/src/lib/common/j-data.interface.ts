export interface JDataFilter {
  field: string;
  operator: string;
  value: string;
  entity: string | undefined;
}
export interface JDataFilterGroup {
  condition: 'AND' | 'OR';
  rules: (JDataFilter | JDataFilterGroup)[];
  valid: boolean;
}

export interface JDataConfig {
  sort: string;
  order: 'ASC' | 'DESC';
  page: number;
  limit: number;
}

export interface JData {
  filter: JDataFilterGroup;
  config: JDataConfig;
}
