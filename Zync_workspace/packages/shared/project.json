{"name": "shared", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/shared/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/shared/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/shared/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": ["scope:lib"]}