import { OpenAPIHono } from '@hono/zod-openapi';
import { ROLES } from '@lib/common/const/roles';
import { MFA_STATUS } from '@lib/common/enums/tfa-status';
import { addMinutes, isBefore } from 'date-fns';
import { badRequestException, unauthorizedException } from '../../../errors/common.error';
import { Dr<PERSON>zleHelper } from '../../common/drizzle-helper';
import { columnListToSelect, dataViewer, mapColumns, paginateQuery } from '../../common/paginator';
import { TABLES } from '../../common/tables.const';
import { Auth, SkipAuth } from '../../guards/auth.guard';
import { useLoginUser } from '../../hooks/auth.hooks';
import { Compress } from '../../receptor/compression.receptor';
import { type APIFileMeta, FileMgrService, type IHasPic } from '../../services/file-mgr.service';
import { PasswordHashEngine } from '../../services/hash.service';
import { inject } from '../../services/services.register';
import { type UserTableSchema, userTokenTable, usersTable } from './auth.entity';
import { AuthService, UserCacheBox, genOTP } from './auth.service';
import {
  loginRoute,
  forgotPasswordRoute,
  resetPasswordRoute,
  updatePasswordRoute,
  verifyEmailRoute,
  updateEmailRoute,
  sendTFAEmailRoute,
  verifyTFAEmailRoute,
  userTFARoute,
  setTFARoute,
  getTFAStatusRoute,
  setTOTPSecretRoute,
  verifyTOTPRoute,
  removeTOTPRoute,
  getAllUsersRoute,
  getAllUsersWithPaginationRoute,
  getAllUserListRoute,
  getUserByIdRoute,
  getSingleUserRoute,
  getUserInfoRoute,
  getProfileRoute,
  createUserRoute,
  createUserByAdminRoute,
  createUserWithTokenRoute,
  updateUserRoute,
  updateUserByAdminRoute,
  updateProfileRoute,
  updateUserProfileRoute,
  removeUserRoute,
  setThumbnailRoute,
} from './auth.routes';

export const createAuthOpenAPIController = () => {
  const app = new OpenAPIHono();
  const authService = inject(AuthService);
  const fileMgrService = inject(FileMgrService);

  // Authentication endpoints
  app.openapi(loginRoute, async (c) => {
    const body = c.req.valid('json');
    const { username, password } = body;
    if (!username || !password) throw badRequestException('Missing username or password');
    
    const user = await authService.validateUser({ username, password });
    if (!user) throw unauthorizedException();
    
    if (user.meta?.TFARequire !== true) {
      return c.json(authService.login(user, body.device));
    }
    
    if (user.secret?.totp) {
      return c.json({ MFAStatus: MFA_STATUS.TOTP_REQUIRED });
    }
    
    // Send TFA email
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, {
      where: { email: user.email },
    });
    
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email: user.email }, { token });
    } else {
      await DrizzleHelper.create(userTokenTable, { token, email: user.email });
    }
    
    authService.sendTFAMail(user, token);
    return c.json({ MFAStatus: MFA_STATUS.OTP_SENT });
  });

  app.openapi(forgotPasswordRoute, async (c) => {
    const body = c.req.valid('json');
    const user = await authService.searchUserForAuth(body.username);
    if (!user) throw badRequestException('Please, check your email and try again');
    
    const token = genOTP(6);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, {
      where: { email: user.email },
    });
    
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email: user.email }, { token });
    } else {
      await DrizzleHelper.create(userTokenTable, { token, email: user.email });
    }
    
    // Send password reset email (implement this in AuthService)
    // await authService.sendPasswordResetMail(user, token);
    
    return c.json({ success: true });
  });

  app.openapi(resetPasswordRoute, async (c) => {
    const body = c.req.valid('json');
    const isValid = await authService.verifyToken({ email: body.token, token: body.token });
    if (!isValid) throw badRequestException('Invalid Token or Token Time Expired');
    
    // Reset password logic here
    return c.json({ success: true });
  });

  app.openapi(updatePasswordRoute, async (c) => {
    const body = c.req.valid('json');
    const user = useLoginUser();
    
    const userData = await DrizzleHelper.findOne(usersTable, { where: { id: user.id } });
    const oldPasswordValid = await PasswordHashEngine.check(body.oldPassword, userData.password);
    if (!oldPasswordValid) throw badRequestException('Please check the old password!!!');
    
    const hashedPassword = await PasswordHashEngine.make(body.password);
    await DrizzleHelper.update(usersTable, { id: user.id }, { password: hashedPassword });
    
    return c.json({ success: true });
  });

  // Email verification endpoints
  app.openapi(verifyEmailRoute, async (c) => {
    const body = c.req.valid('json');
    // Implement email verification logic
    return c.json({ success: true });
  });

  app.openapi(updateEmailRoute, async (c) => {
    const body = c.req.valid('json');
    const user = useLoginUser();
    
    await DrizzleHelper.update(usersTable, { id: user.id }, { email: body.email });
    return c.json({ success: true });
  });

  // Two-Factor Authentication endpoints
  app.openapi(sendTFAEmailRoute, async (c) => {
    const body = c.req.valid('json');
    const user = await authService.searchUserForAuth(body.username);
    if (!user) throw badRequestException('Please, check your email and try again');
    
    const token = genOTP(5);
    const existingToken = await DrizzleHelper.findOne(userTokenTable, {
      where: { email: user.email },
    });
    
    if (existingToken) {
      await DrizzleHelper.update(userTokenTable, { email: user.email }, { token });
    } else {
      await DrizzleHelper.create(userTokenTable, { token, email: user.email });
    }
    
    authService.sendTFAMail(user, token);
    return c.json({ success: true });
  });

  app.openapi(verifyTFAEmailRoute, async (c) => {
    const body = c.req.valid('json');
    const user = await authService.searchUserForAuth(body.username);
    if (!user) throw badRequestException('Please, check your email and try again');
    
    const userToken = await DrizzleHelper.findOne(userTokenTable, { where: { email: user.email } });
    const token = body.token;
    
    if (userToken.token === token && isBefore(new Date(), addMinutes(userToken.updatedAt, 5))) {
      return c.json(authService.login(user, body.device));
    }
    
    throw badRequestException('Invalid Token or Token Time Expired');
  });

  app.openapi(userTFARoute, async (c) => {
    const body = c.req.valid('json');
    const result = await authService.isTFARequire(body.username);
    return c.json({ data: result });
  });

  app.openapi(setTFARoute, async (c) => {
    const body = c.req.valid('json');
    const user = useLoginUser();

    await authService.setTFARequire(user.id, body.enable);
    return c.json({ success: true });
  });

  app.openapi(getTFAStatusRoute, async (c) => {
    const user = useLoginUser();
    return c.json({
      data: { TFARequire: user.meta.TFARequire, TOTPActive: !!user.secret.totp },
    });
  });

  app.openapi(setTOTPSecretRoute, async (c) => {
    const body = c.req.valid('json');
    const user = useLoginUser();
    
    await authService.setTOTPSecret(user.id, body.secret);
    return c.json({ success: true });
  });

  app.openapi(verifyTOTPRoute, async (c) => {
    const body = c.req.valid('json');
    const user = await authService.searchUserForAuth(body.username);
    if (!user) throw badRequestException('User not found');
    
    const res = authService.verifyTOTP(user, body.token);
    if (res) return c.json(authService.login(user, body.device));
    
    throw badRequestException('Invalid Token or Token Time Expired');
  });

  app.openapi(removeTOTPRoute, async (c) => {
    const user = useLoginUser();
    await authService.removeTOTP(user.id);
    return c.json({ success: true });
  });

  // User Management endpoints
  app.openapi(getAllUsersRoute, async (c) => {
    const users = await DrizzleHelper.findAll(usersTable);
    return c.json({ data: users });
  });

  app.openapi(getAllUsersWithPaginationRoute, async (c) => {
    const query = c.req.valid('query');
    const userTable = TABLES.USERS;
    const configs = { ...query };
    const columnList = columnListToSelect(userTable, configs);
    const sqlQuery = dataViewer(userTable, configs, columnList);
    
    const paginatedData = await paginateQuery(sqlQuery, configs, userTable);
    if (paginatedData.data.length) {
      paginatedData.data = paginatedData.data.map(mapColumns(paginatedData.data[0], columnList));
    }
    
    return c.json({ data: paginatedData.data, meta: paginatedData.meta });
  });

  app.openapi(getAllUserListRoute, async (c) => {
    const users = await DrizzleHelper.findAll(usersTable, { select: ['id', 'name', 'pic'] });
    return c.json({ data: users });
  });

  app.openapi(getUserByIdRoute, async (c) => {
    const query = c.req.valid('query');
    const user = await DrizzleHelper.findOne(usersTable, { where: { id: query.id } });
    return c.json({ data: user });
  });

  app.openapi(getSingleUserRoute, async (c) => {
    const query = c.req.valid('query');
    const user = await authService.getUserWithRoles(query.id);
    if (!user) throw badRequestException('User not found');
    
    (user as any).pic = await authService.thumbnail.getThumbnailUrl(user as IHasPic);
    user.password = undefined;
    user.secret = undefined;
    
    return c.json(user);
  });

  app.openapi(getUserInfoRoute, async (c) => {
    const query = c.req.valid('query');
    const user = await DrizzleHelper.findOne(usersTable, { where: { id: query.id } });
    if (!user) throw badRequestException('User not found');
    
    const { name, username } = user;
    const pic = await authService.thumbnail.getThumbnailUrl(user as IHasPic);
    
    return c.json({ data: { id: query.id, name, username, pic } });
  });

  app.openapi(getProfileRoute, async (c) => {
    const user = useLoginUser();
    const userData = await authService.getUserWithRoles(user.id);
    return c.json({ data: userData });
  });

  // User creation and update endpoints
  app.openapi(createUserRoute, async (c) => {
    const body = c.req.valid('json');
    const hashedPassword = await PasswordHashEngine.make(body.password);
    const userData = { ...body, password: hashedPassword };
    const result = await DrizzleHelper.create(usersTable, userData);
    return c.json({ data: result });
  });

  app.openapi(createUserByAdminRoute, async (c) => {
    const body = c.req.valid('json');
    const result = await authService.createUser(body);
    return c.json({ data: result });
  });

  app.openapi(createUserWithTokenRoute, async (c) => {
    const body = c.req.valid('json');
    const isValid = await authService.verifyToken({ email: body.email, token: body.token });
    if (!isValid) throw badRequestException('Invalid Token or Token Time Expired');

    const newUser = await authService.createUser(body);
    await authService.roles.set(newUser.user.id, [body.for]);
    return c.json({ data: newUser });
  });

  app.openapi(updateUserRoute, async (c) => {
    const query = c.req.valid('query');
    const body = c.req.valid('json');

    if (body.password) {
      body.password = await PasswordHashEngine.make(body.password);
    } else {
      delete body.password;
    }

    await DrizzleHelper.update(usersTable, { id: query.id }, body);
    return c.json({ success: true });
  });

  app.openapi(updateUserByAdminRoute, async (c) => {
    const query = c.req.valid('query');
    const body = c.req.valid('json');

    await authService.updateUser(query.id, body);
    return c.json({ success: true });
  });

  app.openapi(updateProfileRoute, async (c) => {
    const query = c.req.valid('query');
    const body = c.req.valid('json');

    const data = await authService.updateUser(query.id, body);
    return c.json({ data });
  });

  app.openapi(updateUserProfileRoute, async (c) => {
    const query = c.req.valid('query');
    const body = c.req.valid('json');

    if (!query.id) throw badRequestException('Invalid User ID');

    const allData = await DrizzleHelper.findOne(usersTable, { where: { id: query.id } });
    if (body.password) {
      const oldPassword = await PasswordHashEngine.check(body.oldPassword, allData.password);
      if (!oldPassword) throw badRequestException('Please check the old password!!!');
    }

    await authService.updateUser(query.id, body);
    return c.json({ success: true });
  });

  app.openapi(removeUserRoute, async (c) => {
    const query = c.req.valid('query');

    UserCacheBox.delete(query.id);
    await DrizzleHelper.destroy(usersTable, { id: query.id });
    return c.json({ success: true });
  });

  app.openapi(setThumbnailRoute, async (c) => {
    const query = c.req.valid('query');
    const body = c.req.valid('json');

    UserCacheBox.delete(query.id);
    const item = await DrizzleHelper.findOne(usersTable, { where: { id: query.id } });
    const res = await authService.thumbnail.setThumbnail(item, body.file);
    return c.json({ data: res });
  });

  return app;
};
