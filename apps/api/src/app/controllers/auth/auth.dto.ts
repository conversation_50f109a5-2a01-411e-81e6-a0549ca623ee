import { z } from 'zod';

// Base User Schema
export const UserSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().nullable(),
  username: z.string().nullable(),
  email: z.string().email(),
  isActive: z.boolean(),
  pic: z.number().int().positive().nullable(),
  meta: z.object({
    TFARequire: z.boolean().optional(),
  }),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Login Schemas
export const LoginRequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  device: z.string().optional(),
});

export const LoginResponseSchema = z.object({
  accessToken: z.string(),
});

export const MFAResponseSchema = z.object({
  MFAStatus: z.enum(['TOTP_REQUIRED', 'OTP_SENT']),
});

// User Creation/Update Schemas
export const CreateUserRequestSchema = z.object({
  name: z.string().optional(),
  username: z.string().optional(),
  email: z.string().email(),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  isActive: z.boolean().optional(),
  meta: z.object({
    TFARequire: z.boolean().optional(),
  }).optional(),
});

export const UpdateUserRequestSchema = z.object({
  name: z.string().optional(),
  username: z.string().optional(),
  email: z.string().email().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  isActive: z.boolean().optional(),
  meta: z.object({
    TFARequire: z.boolean().optional(),
  }).optional(),
});

// Password Management Schemas
export const ForgotPasswordRequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
});

export const ResetPasswordRequestSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const UpdatePasswordRequestSchema = z.object({
  oldPassword: z.string().min(1, 'Old password is required'),
  password: z.string().min(6, 'New password must be at least 6 characters'),
});

// Email Verification Schemas
export const VerifyEmailRequestSchema = z.object({
  token: z.string().min(1, 'Token is required'),
});

export const UpdateEmailRequestSchema = z.object({
  email: z.string().email(),
});

// TFA/MFA Schemas
export const SendTFAEmailRequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
});

export const VerifyTFAEmailRequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  token: z.string().min(1, 'Token is required'),
  device: z.string().optional(),
});

export const UserTFARequestSchema = z.object({
  username: z.string().min(1, 'Username is required'),
});

export const SetTFARequestSchema = z.object({
  enable: z.boolean(),
});

export const SetTOTPSecretRequestSchema = z.object({
  secret: z.string().min(1, 'Secret is required'),
});

export const VerifyTOTPRequestSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  username: z.string().min(1, 'Username is required'),
  device: z.string().optional(),
});

export const TFAStatusResponseSchema = z.object({
  data: z.object({
    TFARequire: z.boolean(),
    TOTPActive: z.boolean(),
  }),
});

// File Upload Schema
export const SetThumbnailRequestSchema = z.object({
  file: z.object({
    id: z.number().int().positive(),
    name: z.string(),
    size: z.number(),
    type: z.string(),
  }),
});

// Query Parameter Schemas
export const IdQuerySchema = z.object({
  id: z.coerce.number().int().positive(),
});

export const PaginationQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Response Schemas
export const SuccessResponseSchema = z.object({
  success: z.boolean(),
});

export const DataResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
  });

export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: z.array(dataSchema),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  });

// Error Response Schema
export const ErrorResponseSchema = z.object({
  error: z.string(),
  message: z.string(),
  statusCode: z.number(),
});

// User with Roles Schema
export const UserWithRolesSchema = UserSchema.extend({
  roleIds: z.array(z.number()).nullable(),
});

// User Info Schema (minimal user data)
export const UserInfoSchema = z.object({
  id: z.number(),
  name: z.string().nullable(),
  username: z.string().nullable(),
  pic: z.string().nullable(), // URL after processing
});

// Admin User Creation Schema
export const CreateUserByAdminRequestSchema = CreateUserRequestSchema.extend({
  roleId: z.array(z.number()).optional(),
});

// User Profile Update Schema
export const UpdateProfileRequestSchema = z.object({
  name: z.string().optional(),
  username: z.string().optional(),
  email: z.string().email().optional(),
  roleId: z.array(z.number()).optional(),
});

export const UpdateUserProfileRequestSchema = UpdateProfileRequestSchema.extend({
  password: z.string().min(6).optional(),
  oldPassword: z.string().optional(),
});

// Type exports for TypeScript
export type LoginRequest = z.infer<typeof LoginRequestSchema>;
export type LoginResponse = z.infer<typeof LoginResponseSchema>;
export type MFAResponse = z.infer<typeof MFAResponseSchema>;
export type CreateUserRequest = z.infer<typeof CreateUserRequestSchema>;
export type UpdateUserRequest = z.infer<typeof UpdateUserRequestSchema>;
export type User = z.infer<typeof UserSchema>;
export type UserWithRoles = z.infer<typeof UserWithRolesSchema>;
export type UserInfo = z.infer<typeof UserInfoSchema>;
export type TFAStatusResponse = z.infer<typeof TFAStatusResponseSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
