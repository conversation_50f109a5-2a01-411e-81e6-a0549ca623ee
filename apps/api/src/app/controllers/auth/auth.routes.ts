import { createRoute } from '@hono/zod-openapi';
import { z } from 'zod';
import {
  LoginRequestSchema,
  LoginResponseSchema,
  MFAResponseSchema,
  CreateUserRequestSchema,
  UpdateUserRequestSchema,
  UserSchema,
  UserWithRolesSchema,
  UserInfoSchema,
  TFAStatusResponseSchema,
  SuccessResponseSchema,
  ErrorResponseSchema,
  DataResponseSchema,
  PaginatedResponseSchema,
  IdQuerySchema,
  PaginationQuerySchema,
  ForgotPasswordRequestSchema,
  ResetPasswordRequestSchema,
  UpdatePasswordRequestSchema,
  VerifyEmailRequestSchema,
  UpdateEmailRequestSchema,
  SendTFAEmailRequestSchema,
  VerifyTFAEmailRequestSchema,
  UserTFARequestSchema,
  SetTFARequestSchema,
  SetTOTPSecretRequestSchema,
  VerifyTOTPRequestSchema,
  SetThumbnailRequestSchema,
  CreateUserByAdminRequestSchema,
  UpdateProfileRequestSchema,
  UpdateUserProfileRequestSchema,
} from './auth.dto';

// Common error responses
const commonErrorResponses = {
  400: {
    content: {
      'application/json': {
        schema: ErrorResponseSchema,
      },
    },
    description: 'Bad Request',
  },
  401: {
    content: {
      'application/json': {
        schema: ErrorResponseSchema,
      },
    },
    description: 'Unauthorized',
  },
  500: {
    content: {
      'application/json': {
        schema: ErrorResponseSchema,
      },
    },
    description: 'Internal Server Error',
  },
};

// Authentication routes
export const loginRoute = createRoute({
  method: 'post',
  path: '/auth/login',
  tags: ['Authentication'],
  summary: 'User login',
  description: 'Authenticate user with username/email and password. Returns access token or MFA challenge.',
  request: {
    body: {
      content: {
        'application/json': {
          schema: LoginRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: LoginResponseSchema.or(MFAResponseSchema),
        },
      },
      description: 'Login successful or MFA required',
    },
    ...commonErrorResponses,
  },
});

export const forgotPasswordRoute = createRoute({
  method: 'post',
  path: '/auth/forgot-password',
  tags: ['Authentication'],
  summary: 'Forgot password',
  description: 'Send password reset email to user',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ForgotPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'Password reset email sent',
    },
    ...commonErrorResponses,
  },
});

export const resetPasswordRoute = createRoute({
  method: 'post',
  path: '/auth/reset-pwd',
  tags: ['Authentication'],
  summary: 'Reset password',
  description: 'Reset user password using reset token',
  request: {
    body: {
      content: {
        'application/json': {
          schema: ResetPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'Password reset successful',
    },
    ...commonErrorResponses,
  },
});

export const updatePasswordRoute = createRoute({
  method: 'post',
  path: '/auth/update-pwd',
  tags: ['Authentication'],
  summary: 'Update password',
  description: 'Update user password (requires authentication)',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: UpdatePasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'Password updated successfully',
    },
    ...commonErrorResponses,
  },
});

// Email verification routes
export const verifyEmailRoute = createRoute({
  method: 'post',
  path: '/auth/verify-email',
  tags: ['Email Verification'],
  summary: 'Verify email',
  description: 'Verify user email address using verification token',
  request: {
    body: {
      content: {
        'application/json': {
          schema: VerifyEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'Email verified successfully',
    },
    ...commonErrorResponses,
  },
});

export const updateEmailRoute = createRoute({
  method: 'post',
  path: '/auth/update-email',
  tags: ['Email Verification'],
  summary: 'Update email',
  description: 'Update user email address (requires authentication)',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: UpdateEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'Email updated successfully',
    },
    ...commonErrorResponses,
  },
});

// Two-Factor Authentication routes
export const sendTFAEmailRoute = createRoute({
  method: 'post',
  path: '/auth/send-tfa-email',
  tags: ['Two-Factor Authentication'],
  summary: 'Send TFA email',
  description: 'Send two-factor authentication code via email',
  request: {
    body: {
      content: {
        'application/json': {
          schema: SendTFAEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'TFA email sent successfully',
    },
    ...commonErrorResponses,
  },
});

export const verifyTFAEmailRoute = createRoute({
  method: 'post',
  path: '/auth/verify-tfa-email',
  tags: ['Two-Factor Authentication'],
  summary: 'Verify TFA email',
  description: 'Verify two-factor authentication code from email',
  request: {
    body: {
      content: {
        'application/json': {
          schema: VerifyTFAEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: LoginResponseSchema,
        },
      },
      description: 'TFA verification successful, login complete',
    },
    ...commonErrorResponses,
  },
});

export const userTFARoute = createRoute({
  method: 'post',
  path: '/auth/user-tfa',
  tags: ['Two-Factor Authentication'],
  summary: 'Check user TFA status',
  description: 'Check if user has two-factor authentication enabled',
  request: {
    body: {
      content: {
        'application/json': {
          schema: UserTFARequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(TFAStatusResponseSchema),
        },
      },
      description: 'TFA status retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const setTFARoute = createRoute({
  method: 'post',
  path: '/auth/set-tfa',
  tags: ['Two-Factor Authentication'],
  summary: 'Enable/disable TFA',
  description: 'Enable or disable two-factor authentication for user',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: SetTFARequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'TFA setting updated successfully',
    },
    ...commonErrorResponses,
  },
});

export const getTFAStatusRoute = createRoute({
  method: 'get',
  path: '/auth/get-tfa-status',
  tags: ['Two-Factor Authentication'],
  summary: 'Get TFA status',
  description: 'Get current user\'s two-factor authentication status',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: TFAStatusResponseSchema,
        },
      },
      description: 'TFA status retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const setTOTPSecretRoute = createRoute({
  method: 'post',
  path: '/auth/set-totp-secret-tfa',
  tags: ['Two-Factor Authentication'],
  summary: 'Set TOTP secret',
  description: 'Set TOTP secret for time-based one-time password authentication',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: SetTOTPSecretRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'TOTP secret set successfully',
    },
    ...commonErrorResponses,
  },
});

export const verifyTOTPRoute = createRoute({
  method: 'post',
  path: '/auth/verify-totp',
  tags: ['Two-Factor Authentication'],
  summary: 'Verify TOTP',
  description: 'Verify time-based one-time password for authentication',
  request: {
    body: {
      content: {
        'application/json': {
          schema: VerifyTOTPRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: LoginResponseSchema,
        },
      },
      description: 'TOTP verification successful, login complete',
    },
    ...commonErrorResponses,
  },
});

export const removeTOTPRoute = createRoute({
  method: 'post',
  path: '/auth/remove-totp',
  tags: ['Two-Factor Authentication'],
  summary: 'Remove TOTP',
  description: 'Remove TOTP authentication for current user',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'TOTP removed successfully',
    },
    ...commonErrorResponses,
  },
});

// User Management routes
export const getAllUsersRoute = createRoute({
  method: 'get',
  path: '/auth/get-all',
  tags: ['User Management'],
  summary: 'Get all users',
  description: 'Retrieve all users (admin only)',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema.array()),
        },
      },
      description: 'Users retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getAllUsersWithPaginationRoute = createRoute({
  method: 'get',
  path: '/auth/get-all-users',
  tags: ['User Management'],
  summary: 'Get all users with pagination',
  description: 'Retrieve all users with pagination and filtering',
  security: [{ bearerAuth: [] }],
  request: {
    query: PaginationQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: PaginatedResponseSchema(UserSchema),
        },
      },
      description: 'Users retrieved successfully with pagination',
    },
    ...commonErrorResponses,
  },
});

export const getAllUserListRoute = createRoute({
  method: 'get',
  path: '/auth/get-all-user',
  tags: ['User Management'],
  summary: 'Get user list',
  description: 'Get simplified user list (id, name, pic only)',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserInfoSchema.array()),
        },
      },
      description: 'User list retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getUserByIdRoute = createRoute({
  method: 'get',
  path: '/auth/get-by-id',
  tags: ['User Management'],
  summary: 'Get user by ID',
  description: 'Retrieve user information by ID',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema),
        },
      },
      description: 'User retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getSingleUserRoute = createRoute({
  method: 'get',
  path: '/auth/get-single-user',
  tags: ['User Management'],
  summary: 'Get single user with roles',
  description: 'Retrieve user information with roles by ID',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: UserWithRolesSchema,
        },
      },
      description: 'User with roles retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getUserInfoRoute = createRoute({
  method: 'get',
  path: '/auth/get-user-info',
  tags: ['User Management'],
  summary: 'Get user info',
  description: 'Get basic user information (id, name, username, pic)',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserInfoSchema),
        },
      },
      description: 'User info retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getProfileRoute = createRoute({
  method: 'get',
  path: '/auth/get-profile',
  tags: ['User Profile'],
  summary: 'Get current user profile',
  description: 'Get current authenticated user\'s profile information',
  security: [{ bearerAuth: [] }],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserWithRolesSchema),
        },
      },
      description: 'Profile retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const createUserRoute = createRoute({
  method: 'post',
  path: '/auth/create',
  tags: ['User Management'],
  summary: 'Create user',
  description: 'Create a new user account',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: CreateUserRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema),
        },
      },
      description: 'User created successfully',
    },
    ...commonErrorResponses,
  },
});

export const createUserByAdminRoute = createRoute({
  method: 'post',
  path: '/auth/create-user-by-admin',
  tags: ['User Management'],
  summary: 'Create user by admin',
  description: 'Create a new user account by admin with role assignment',
  security: [{ bearerAuth: [] }],
  request: {
    body: {
      content: {
        'application/json': {
          schema: CreateUserByAdminRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema),
        },
      },
      description: 'User created by admin successfully',
    },
    ...commonErrorResponses,
  },
});

export const createUserWithTokenRoute = createRoute({
  method: 'post',
  path: '/auth/create-user',
  tags: ['User Management'],
  summary: 'Create user with token',
  description: 'Create user account using verification token',
  request: {
    body: {
      content: {
        'application/json': {
          schema: CreateUserByAdminRequestSchema.extend({
            token: z.string().min(1, 'Token is required'),
            for: z.number().int().positive(),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema),
        },
      },
      description: 'User created with token successfully',
    },
    ...commonErrorResponses,
  },
});

export const updateUserRoute = createRoute({
  method: 'post',
  path: '/auth/update',
  tags: ['User Management'],
  summary: 'Update user',
  description: 'Update user information by ID',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
    body: {
      content: {
        'application/json': {
          schema: UpdateUserRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'User updated successfully',
    },
    ...commonErrorResponses,
  },
});

export const updateUserByAdminRoute = createRoute({
  method: 'post',
  path: '/auth/update-user',
  tags: ['User Management'],
  summary: 'Update user by admin',
  description: 'Update user information by admin with role management',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
    body: {
      content: {
        'application/json': {
          schema: UpdateProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'User updated by admin successfully',
    },
    ...commonErrorResponses,
  },
});

export const updateProfileRoute = createRoute({
  method: 'post',
  path: '/auth/update-profile',
  tags: ['User Profile'],
  summary: 'Update user profile',
  description: 'Update current user\'s profile information',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
    body: {
      content: {
        'application/json': {
          schema: UpdateProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(UserSchema),
        },
      },
      description: 'Profile updated successfully',
    },
    ...commonErrorResponses,
  },
});

export const updateUserProfileRoute = createRoute({
  method: 'post',
  path: '/auth/update-user-profile',
  tags: ['User Profile'],
  summary: 'Update user profile with password',
  description: 'Update user profile including password change',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
    body: {
      content: {
        'application/json': {
          schema: UpdateUserProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'User profile updated successfully',
    },
    ...commonErrorResponses,
  },
});

export const removeUserRoute = createRoute({
  method: 'post',
  path: '/auth/remove',
  tags: ['User Management'],
  summary: 'Remove user',
  description: 'Delete user account by ID',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: SuccessResponseSchema,
        },
      },
      description: 'User removed successfully',
    },
    ...commonErrorResponses,
  },
});

export const setThumbnailRoute = createRoute({
  method: 'post',
  path: '/auth/set-thumbnail',
  tags: ['User Profile'],
  summary: 'Set user thumbnail',
  description: 'Upload and set user profile picture',
  security: [{ bearerAuth: [] }],
  request: {
    query: IdQuerySchema,
    body: {
      content: {
        'application/json': {
          schema: SetThumbnailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: DataResponseSchema(z.object({
            url: z.string(),
            id: z.number(),
          })),
        },
      },
      description: 'Thumbnail set successfully',
    },
    ...commonErrorResponses,
  },
});
