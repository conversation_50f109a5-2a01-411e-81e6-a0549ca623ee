import { OpenAPIHono } from '@hono/zod-openapi';
import { swaggerUI } from '@hono/swagger-ui';

export const createStandaloneSwagger = () => {
  const app = new OpenAPIHono();

  // OpenAPI specification for AuthController
  const openAPISpec = {
    openapi: '3.0.0',
    info: {
      version: '1.0.0',
      title: 'Zync PropTech API - Authentication',
      description: 'Comprehensive API documentation for Zync PropTech Authentication endpoints',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: 'http://localhost:3333/api',
        description: 'Development server',
      },
      {
        url: 'https://api.zyncproptech.com/api',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication. Format: Bearer <token>',
        },
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'integer', example: 1 },
            name: { type: 'string', example: '<PERSON> Doe', nullable: true },
            username: { type: 'string', example: 'johndoe', nullable: true },
            email: { type: 'string', format: 'email', example: '<EMAIL>' },
            isActive: { type: 'boolean', example: true },
            pic: { type: 'integer', example: 123, nullable: true },
            meta: {
              type: 'object',
              properties: {
                TFARequire: { type: 'boolean', example: false },
              },
            },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        LoginRequest: {
          type: 'object',
          required: ['username', 'password'],
          properties: {
            username: { type: 'string', example: 'johndoe' },
            password: { type: 'string', example: 'password123' },
            device: { type: 'string', example: 'web' },
          },
        },
        LoginResponse: {
          type: 'object',
          properties: {
            accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
          },
        },
        MFAResponse: {
          type: 'object',
          properties: {
            MFAStatus: { type: 'string', enum: ['TOTP_REQUIRED', 'OTP_SENT'] },
          },
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
          },
        },
        ErrorResponse: {
          type: 'object',
          properties: {
            error: { type: 'string', example: 'Bad Request' },
            message: { type: 'string', example: 'Invalid input data' },
            statusCode: { type: 'integer', example: 400 },
          },
        },
        DataResponse: {
          type: 'object',
          properties: {
            data: { type: 'object' },
          },
        },
        TFAStatusResponse: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                TFARequire: { type: 'boolean', example: false },
                TOTPActive: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and login operations',
      },
      {
        name: 'User Management',
        description: 'User CRUD operations and administration',
      },
      {
        name: 'User Profile',
        description: 'User profile management and settings',
      },
      {
        name: 'Two-Factor Authentication',
        description: 'Two-factor authentication (2FA) and TOTP operations',
      },
      {
        name: 'Email Verification',
        description: 'Email verification and management',
      },
    ],
    paths: {
      '/auth/login': {
        post: {
          tags: ['Authentication'],
          summary: 'User login',
          description: 'Authenticate user with username/email and password. Returns access token or MFA challenge.',
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/LoginRequest' },
              },
            },
          },
          responses: {
            '200': {
              description: 'Login successful or MFA required',
              content: {
                'application/json': {
                  schema: {
                    oneOf: [
                      { $ref: '#/components/schemas/LoginResponse' },
                      { $ref: '#/components/schemas/MFAResponse' },
                    ],
                  },
                },
              },
            },
            '400': {
              description: 'Bad Request',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
            '401': {
              description: 'Unauthorized',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
          },
        },
      },
      '/auth/get-all': {
        get: {
          tags: ['User Management'],
          summary: 'Get all users',
          description: 'Retrieve all users (admin only)',
          security: [{ bearerAuth: [] }],
          responses: {
            '200': {
              description: 'Users retrieved successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: {
                        type: 'array',
                        items: { $ref: '#/components/schemas/User' },
                      },
                    },
                  },
                },
              },
            },
            '401': {
              description: 'Unauthorized',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
          },
        },
      },
      '/auth/get-by-id': {
        get: {
          tags: ['User Management'],
          summary: 'Get user by ID',
          description: 'Retrieve user information by ID',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'id',
              in: 'query',
              required: true,
              schema: { type: 'integer', minimum: 1 },
              description: 'User ID',
            },
          ],
          responses: {
            '200': {
              description: 'User retrieved successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/User' },
                    },
                  },
                },
              },
            },
            '400': {
              description: 'Bad Request',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
            '401': {
              description: 'Unauthorized',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
          },
        },
      },
      '/auth/get-profile': {
        get: {
          tags: ['User Profile'],
          summary: 'Get current user profile',
          description: 'Get current authenticated user\'s profile information',
          security: [{ bearerAuth: [] }],
          responses: {
            '200': {
              description: 'Profile retrieved successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/User' },
                    },
                  },
                },
              },
            },
            '401': {
              description: 'Unauthorized',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
          },
        },
      },
      '/auth/get-tfa-status': {
        get: {
          tags: ['Two-Factor Authentication'],
          summary: 'Get TFA status',
          description: 'Get current user\'s two-factor authentication status',
          security: [{ bearerAuth: [] }],
          responses: {
            '200': {
              description: 'TFA status retrieved successfully',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/TFAStatusResponse' },
                },
              },
            },
            '401': {
              description: 'Unauthorized',
              content: {
                'application/json': {
                  schema: { $ref: '#/components/schemas/ErrorResponse' },
                },
              },
            },
          },
        },
      },
    },
  };

  // Serve OpenAPI spec
  app.get('/doc', (c) => {
    return c.json(openAPISpec);
  });

  // Serve Swagger UI
  app.get('/ui', swaggerUI({ url: '/api/swagger/doc' }));

  return app;
};
