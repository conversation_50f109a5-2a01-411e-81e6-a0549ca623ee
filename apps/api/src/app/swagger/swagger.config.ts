import { OpenAPIHono } from '@hono/zod-openapi';
import { swaggerUI } from '@hono/swagger-ui';
import { createAuthOpenAPIController } from '../controllers/auth/auth.openapi.controller';

export const createSwaggerApp = () => {
  const app = new OpenAPIHono();

  // Configure OpenAPI document
  app.doc('/doc', {
    openapi: '3.0.0',
    info: {
      version: '1.0.0',
      title: 'Zync PropTech API',
      description: 'Comprehensive API documentation for Zync PropTech application',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: 'http://localhost:3333/api',
        description: 'Development server',
      },
      {
        url: 'https://api.zyncproptech.com/api',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for authentication. Format: Bearer <token>',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and login operations',
      },
      {
        name: 'User Management',
        description: 'User CRUD operations and administration',
      },
      {
        name: 'User Profile',
        description: 'User profile management and settings',
      },
      {
        name: 'Two-Factor Authentication',
        description: 'Two-factor authentication (2FA) and TOTP operations',
      },
      {
        name: 'Email Verification',
        description: 'Email verification and management',
      },
    ],
    externalDocs: {
      description: 'Find more info here',
      url: 'https://docs.zyncproptech.com',
    },
  });

  // Add Swagger UI
  app.get('/ui', swaggerUI({ url: '/api/swagger/doc' }));

  // Mount auth controller
  const authController = createAuthOpenAPIController();
  app.route('/auth', authController);

  return app;
};

// Export individual route groups for modular usage
export { createAuthOpenAPIController };
