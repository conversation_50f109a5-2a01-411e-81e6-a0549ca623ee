import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, StatusBar, Dimensions, Platform } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import SearchBar from '../../components/common/SearchBar';
import Ionicons from 'react-native-vector-icons/Ionicons';

const AllChatsScreen: React.FC = () => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [headerHeight, setHeaderHeight] = useState(0);
  const [screenHeight, setScreenHeight] = useState(0);

  useEffect(() => {
    const calculateHeaderHeight = () => {
      const { height } = Dimensions.get('window');
      const statusBarHeight = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 24;
      const headerContentHeight = Platform.OS === 'ios' ? 20 : 56; // Increased height for Android
      const totalHeaderHeight = statusBarHeight + headerContentHeight;
      
      setScreenHeight(height);
      setHeaderHeight(totalHeaderHeight);
    };

    calculateHeaderHeight();
    
    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', calculateHeaderHeight);
    
    return () => subscription?.remove();
  }, []);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    // TODO: Implement search functionality
    console.log('Searching chats for:', query);
  };

  const handleCreateChat = () => {
    // TODO: Navigate to create chat screen
    console.log('Create chat pressed');
  };  

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <StatusBar 
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.isDark ? '#000000' : '#FFFFFF'}
      />
      {/* Dynamic Header */}
      <View 
        className={`flex-row justify-between items-center px-4 mt-2 ${theme.colors.background}`}
        style={{ 
          height: Platform.OS === 'ios' ? headerHeight : 'auto',
          paddingTop: Platform.OS === 'ios' ? 20 : StatusBar.currentHeight || 24,
          paddingBottom: Platform.OS === 'ios' ? 4 : 12,
        }}
      >
        <Text className={`text-2xl font-bold ${theme.colors.text}`}>
          Chats
        </Text>
        <TouchableOpacity
          className={`px-4 py-2 rounded-lg ${theme.colors.primary} flex-row items-center justify-center`}
          style={{ display: 'none' }}
          onPress={handleCreateChat}
        >
          <Ionicons name="add" size={20} color="white" />
          <Text className="text-white font-medium text-sm">Create</Text>
        </TouchableOpacity> 
      </View>

      <ScrollView className="flex-1 px-2">
        <SearchBar 
          placeholder="Search chats..."
          onSearch={handleSearch}
        />
        
        <View className="flex-1 justify-center items-center py-8">
          <Text className={`text-center ${theme.colors.textSecondary} text-lg`}>
           No chats found.
          </Text>
          {searchQuery.length > 0 && (
            <Text className={`text-center ${theme.colors.textSecondary} text-sm mt-4`}>
              Searching for: "{searchQuery}"
            </Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AllChatsScreen;