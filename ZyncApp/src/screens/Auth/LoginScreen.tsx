import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView, 
  ImageBackground, 
  Dimensions, 
  Image, 
  ActivityIndicator, 
  Platform,
  ScrollView,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { validateEmail, validatePassword } from '../../utils/validators';
import authServices from '../../services/authServices';


// Constants & Types
const { height } = Dimensions.get('window');

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Main LoginScreen Component
const LoginScreen: React.FC = () => {
  // State Management
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');

  // Hooks & Context
  const navigation = useNavigation<NavigationProp>();
  const { login } = useAuth();
  const { theme } = useTheme();
  
  // Ref to track if component is mounted
  const isMounted = useRef(true);

  // Load saved credentials on component mount
  useEffect(() => {
    loadSavedCredentials();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Cleanup states when navigating away
  useEffect(() => {
    const unsubscribe = navigation.addListener('blur', () => {
      // Reset all states when leaving the screen
      if (isMounted.current) {
        setIsLoading(false);
        setLoginError('');
        setLoginSuccess('');
        setEmailError('');
        setPasswordError('');
      }
    });

    return unsubscribe;
  }, [navigation]);

  // Helper Functions
  // Load saved credentials from AsyncStorage
  const loadSavedCredentials = async () => {
    try {
      const savedEmail = await AsyncStorage.getItem('remembered_email');
      const savedPassword = await AsyncStorage.getItem('remembered_password');
      const savedRememberMe = await AsyncStorage.getItem('remember_me');
      
      if (savedEmail && savedPassword && savedRememberMe === 'true') {
        setEmail(savedEmail);
        setPassword(savedPassword);
        setRememberMe(true);
      }
    } catch (error) {
      console.error('Error loading saved credentials:', error);
    }
  };

  // Save credentials to AsyncStorage
  const saveCredentials = async (email: string, password: string) => {
    try {
      await AsyncStorage.multiSet([
        ['remembered_email', email],
        ['remembered_password', password],
        ['remember_me', 'true']
      ]);
    } catch (error) {
      console.error('Error saving credentials:', error);
    }
  };

  // Clear saved credentials from AsyncStorage
  const clearSavedCredentials = async () => {
    try {
      await AsyncStorage.multiRemove([
        'remembered_email',
        'remembered_password',
        'remember_me'
      ]);
    } catch (error) {
      console.error('Error clearing saved credentials:', error);
    }
  };

  // Event Handlers
  // Handles email input changes and clears email errors
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (emailError) {
      setEmailError('');
    }
    // Clear login error and success messages when user starts typing
    if (loginError) {
      setLoginError('');
    }
    if (loginSuccess) {
      setLoginSuccess('');
    }
  };

  // Handles password input changes and clears password errors
  const handlePasswordChange = (text: string) => {
    setPassword(text);
    if (passwordError) {
      setPasswordError('');
    }
    // Clear login error and success messages when user starts typing
    if (loginError) {
      setLoginError('');
    }
    if (loginSuccess) {
      setLoginSuccess('');
    }
  };

  // Handles remember me checkbox toggle
  const handleRememberMeToggle = async () => {
    const newRememberMe = !rememberMe;
    setRememberMe(newRememberMe);
    
    if (!newRememberMe) {
      // If unchecking remember me, clear saved credentials
      await clearSavedCredentials();
    }
  };

  // Handles the login form submission with validation
  const handleLogin = async () => {
    // Clear all previous errors
    setEmailError('');
    setPasswordError('');
    setLoginError('');

    // Validate email field
    let hasEmailError = false;
    if (!email) {
      setEmailError('Email is required');
      hasEmailError = true;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      hasEmailError = true;
    }

    // Validate password field
    let hasPasswordError = false;
    if (!password) {
      setPasswordError('Password is required');
      hasPasswordError = true;
    } else {
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        setPasswordError(passwordValidation.errors[0]); // Show first error
        hasPasswordError = true;
      }
    }

    // Stop if validation errors exist
    if (hasEmailError || hasPasswordError) {
      return;
    }

    // Attempt login
    setIsLoading(true);
    setLoginError('');
    setLoginSuccess('');
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      // First, call authServices to check for specific error cases
      const response = await authServices.login({ email, password });

      if (response.success && response.data) {
        // Check if account needs email verification (is_active: 0)
        if (response.data.is_active === 0) {
          setLoginError(''); // Clear any error
          setLoginSuccess('Account created. Verify via email OTP. Redirecting….');
          // Don't set loading to false here - keep loader until redirect
          // Keep loader active until navigation
          const timer = setTimeout(() => {
            // Only navigate if component is still mounted
            if (isMounted.current) {
              navigation.navigate('auth/otp-verification', {
                email,
                fromScreen: 'login',
                message: 'Please verify your email to activate your account'
              });
            }
          }, 2000); // Show message for 2 seconds then redirect

          // Clear the timer if component unmounts
          return () => {
            clearTimeout(timer);
            if (isMounted.current) {
              setIsLoading(false);
              setLoginError('');
              setLoginSuccess('');
            }
          };
        }

        // Check for wrong auth provider
        if (response.data.auth_provider && response.data.auth_provider !== 'manual') {
          setLoginError(`You registered using ${response.data.auth_provider} authentication. Please use the same provider to log in.`);
          setIsLoading(false); // Stop loading on error
          return;
        }

        // If all checks pass, update the authentication state
        const success = await login(email, password);
        if (success) {
          // Save credentials if remember me is checked
          if (rememberMe) {
            await saveCredentials(email, password);
          } else {
            // Clear any previously saved credentials
            await clearSavedCredentials();
          }
          
          // Navigation will happen automatically when isAuthenticated becomes true
          console.log('✅ [LoginScreen] Login successful, authentication state updated');
          // Keep loading state active - AuthContext will handle navigation
        } else {
          setLoginError('Authentication failed. Please try again.');
          setIsLoading(false); // Stop loading on error
        }
      } else {
        // Handle specific error messages from backend
        setLoginError(response.error || 'Login failed. Please try again.');
        setIsLoading(false); // Stop loading on error
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setLoginError('Network error. Please check your connection and try again.');
      setIsLoading(false); // Only stop loading on error
    }
  };

  // Render Methods
  // Renders the email input field with validation error display
  const renderEmailField = () => (
    <View className="mb-4">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${emailError ? 'border-red-500' : ''}`}
        placeholder="Email"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      {emailError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{emailError}</Text>
      ) : null}
    </View>
  );

  // Renders the password input field with show/hide toggle
  const renderPasswordField = () => (
    <View className="mb-6">
      <View className="relative">
        <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-4 ${theme.colors.text} text-base ${passwordError ? 'border-red-500' : ''} pr-12`}
          placeholder="Password"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={password}
          onChangeText={handlePasswordChange}
          secureTextEntry={!showPassword}
        />
        <TouchableOpacity
          className="absolute right-3 top-0 bottom-0 justify-center"
          onPress={() => setShowPassword(!showPassword)}
        >
          <Ionicons 
            name={showPassword ? "eye-off" : "eye"} 
            size={20} 
            color={theme.isDark ? '#9CA3AF' : '#6B7280'} 
          />
        </TouchableOpacity>
      </View>
      {passwordError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{passwordError}</Text>
      ) : null}
    </View>
  );

  // Renders the login button with loading state
  const renderLoginButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleLogin}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Signing In...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Login
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders social login buttons (Google, Apple, Microsoft)
  const renderSocialLoginButtons = () => (
    <View className="mb-6">
      {/* Google Login Button */}
      <View className="mb-2">
        <TouchableOpacity 
          className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center opacity-50`}
          disabled={true}
        >
          <Image 
            source={require('../../assets/images/auth/google.png')}
            style={{ width: 20, height: 20, marginRight: 8, opacity: 0.6 }}
            resizeMode="contain"
          />
          <Text className={`font-medium ${theme.colors.textSecondary}`}>Continue with Google</Text>
        </TouchableOpacity>
      </View>

      {/* Apple Login Button - Only show on iOS */}
      {Platform.OS === 'ios' && (
        <View className="mb-2">
          <TouchableOpacity 
            className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center opacity-50`}
            disabled={true}
          >
            <Icon name="apple" size={20} color={theme.isDark ? '#6B7280' : '#9CA3AF'} style={{ marginRight: 8 }} />
            <Text className={`font-medium ${theme.colors.textSecondary}`}>Continue with Apple</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Microsoft Login Button */}
      <View className="mb-2">
        <TouchableOpacity 
          className={`${theme.colors.surface} border ${theme.colors.border} rounded-lg py-3 px-4 flex-row items-center justify-center opacity-50`}
          disabled={true}
        >
          <Image 
            source={require('../../assets/images/auth/microsoft.png')}
            style={{ width: 20, height: 20, marginRight: 8, opacity: 0.6 }}
            resizeMode="contain"
          />
          <Text className={`font-medium ${theme.colors.textSecondary}`}>Continue with Microsoft Account</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView 
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Background Image Section */}
          <View style={{ height: height * 0.26 }}>
            <ImageBackground
              source={require('../../assets/images/auth/auth_bg.png')}
              style={{ flex: 1 }}
              resizeMode="cover"
            />
          </View>

          {/* Login Form Container */}
          <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
            {/* Welcome Header */}
            <View className="mb-4">
              <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
                Welcome Back!
              </Text>
            </View>

            {/* Form Fields */}
            {renderEmailField()}
            {renderPasswordField()}

            {/* Login Messages Display */}
            {(loginError || loginSuccess) ? (
              <View className="mb-4">
                <Text className={`text-sm text-center ${
                  loginSuccess || loginError.includes('successful') || loginError.includes('success') || loginError.includes('Redirecting')
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {loginSuccess || loginError}
                </Text>
              </View>
            ) : null}

            {/* Login Button */}
            {renderLoginButton()}

            {/* Remember Me & Forgot Password */}
            <View className="flex-row justify-between items-center mb-4">
              <View className="flex-row items-center">
                <TouchableOpacity 
                  className={`w-5 h-5 border-2 rounded mr-2 items-center justify-center ${rememberMe ? 'bg-blue-500 border-blue-500' : `border-gray-400 ${theme.isDark ? 'bg-gray-700' : 'bg-white'}`}`}
                  onPress={handleRememberMeToggle}
                >
                  {rememberMe && (
                    <Ionicons name="checkmark" size={14} color="white" />
                  )}
                </TouchableOpacity>
                <Text className={`text-sm ${theme.colors.text}`}>Remember Me</Text>
              </View>
              <TouchableOpacity onPress={() => navigation.navigate('auth/forgot-password')}>
                <Text className="text-red-500 text-sm font-medium">
                  Forgot Your Password?
                </Text>
              </TouchableOpacity>
            </View>

            {/* Social Login Options */}
            {renderSocialLoginButtons()}

            {/* Sign Up Link */}
            <View className="flex-row justify-center">
              <Text className={theme.colors.textSecondary}>
                Don't have an account?{' '}
              </Text>
              <TouchableOpacity onPress={() => navigation.navigate('auth/register')}>
                <Text className="text-blue-500 font-medium">
                  Sign Up
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
