import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { validateEmail } from '../../utils/validators';
import authServices from '../../services/authServices';
// ZYNC 


// Constants
const { height } = Dimensions.get('window');

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Main Forgot Password Component
const ForgotPasswordScreen: React.FC = () => {
  // Form data
  const [email, setEmail] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  
  // Error states
  const [emailError, setEmailError] = useState('');
  const [submitError, setSubmitError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();

  // Event Handlers
  // Handles email input changes and clears errors
  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (emailError) {
      setEmailError('');
    }
  };

  // Handles the forgot password form submission with validation
  const handleSubmit = async () => {
    // Clear previous errors
    setEmailError('');
    setSubmitError('');

    // Validate email
    if (!email) {
      setEmailError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    // Attempt to send reset email
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      const response = await authServices.forgotPassword({ email });

      if (response.success && response.data) {
        // Navigate to OTP verification for password reset
        navigation.navigate('auth/forgot-password-otp', {
          email,
          message: response.data.message || 'Password reset OTP sent to your email.'
        });
      } else {
        // Handle specific error messages from backend
        setSubmitError(response.error || 'Failed to send reset email. Please try again.');
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      setSubmitError('Network error. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render Methods
  // Renders the email input field with validation error display
  const renderEmailField = () => (
    <View className="mb-6">
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-base ${emailError ? 'border-red-500' : ''}`}
        placeholder="Email"
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
        style={{ textAlignVertical: 'center' }}
      />
      {emailError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1">{emailError}</Text>
      ) : null}
    </View>
  );

  // Renders the submit button with loading state
  const renderSubmitButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleSubmit}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Sending...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Send Reset Email
        </Text>
      )}
    </TouchableOpacity>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView 
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Background Image Section */}
          <View style={{ height: height * 0.26 }}>
            <ImageBackground
              source={require('../../assets/images/auth/auth_bg.png')}
              style={{ flex: 1 }}
              resizeMode="cover"
            />
          </View>

          {/* Forgot Password Form Container */}
          <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
            {/* Welcome Header */}
            <View className="mb-4">
              <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
                Forgot Password
              </Text>
              <Text className={`text-start ${theme.colors.textSecondary}`}>
                Enter your email to reset your password
              </Text>
            </View>

            {/* Form Fields */}
            {renderEmailField()}

            {/* Submit Error Display */}
            {submitError ? (
              <View className="mb-4">
                <Text className={`text-sm text-center ${
                  submitError.includes('successful') || submitError.includes('success')
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {submitError}
                </Text>
              </View>
            ) : null}

            {/* Submit Button */}
            {renderSubmitButton()}

            {/* Back to Login Link */}
            <View className="flex-row justify-center">
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Text className="text-blue-500 font-medium">
                  Back to Login
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
