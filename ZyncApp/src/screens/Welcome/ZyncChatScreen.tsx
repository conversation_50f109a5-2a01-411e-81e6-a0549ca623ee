import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, TextInput, ScrollView, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { useTheme } from '../../context/ThemeContext';
import Icon from 'react-native-vector-icons/FontAwesome';
// ZYNC 


type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ZyncChatScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const [searchText, setSearchText] = useState('');
  const [messageText, setMessageText] = useState('');

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* body  */}
      <View className={`flex-1 ${theme.colors.background}`} style={{ marginTop: 180 }}>
        {/* Top Header */}
        <View className="flex-row items-center justify-between px-6 py-3">
          <TouchableOpacity>
            <Icon name="search" size={18} color="white" />
          </TouchableOpacity>
          <Text className="text-white text-lg font-bold">ZYNC Chat</Text>
          <TouchableOpacity>
            <Icon name="ellipsis-v" size={18} color="white" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="px-6 mb-2">
          <View className={`flex-row items-center rounded-xl px-4 py-2.5 ${theme.colors.surface}`}>
            <Icon name="search" size={14} color={theme.isDark ? "#9CA3AF" : "#6B7280"} style={{ marginRight: 8 }} />
            <TextInput
              className={`flex-1 text-sm ${theme.colors.text}`}
              placeholder="Search"
              placeholderTextColor={theme.isDark ? "#9CA3AF" : "#6B7280"}
              value={searchText}
              onChangeText={setSearchText}
            />
          </View>
        </View>

        {/* Chat List and Input Area */}
        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>

          {/* Oliver Chat */}
          <TouchableOpacity className={`flex-row items-center py-2.5 border-b ${theme.colors.border}`}>
            <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
              <Text className="text-white font-bold text-base">O</Text>
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>Oliver</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>Good Morning</Text>
            </View>
            <View className="bg-blue-500 rounded-md px-2 py-0.5">
              <Text className="text-white text-xs font-bold">AI</Text>
            </View>
          </TouchableOpacity>

          {/* Mason Chat */}
          <TouchableOpacity className={`flex-row items-center py-2.5 border-b ${theme.colors.border}`}>
            <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
              <Text className="text-white font-bold text-base">M</Text>
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>Mason</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>Where are you?</Text>
            </View>
            <View className="flex-row space-x-0.5">
              <Text className="text-sm">😍</Text>
              <Text className="text-sm">👍</Text>
              <Text className="text-sm">🐦</Text>
              <Text className="text-sm">👍</Text>
            </View>
          </TouchableOpacity>

          {/* Sophia Chat */}
          <TouchableOpacity className={`flex-row items-center py-2.5 border-b ${theme.colors.border} mb-10`}>
            <View className="w-10 h-10 bg-blue-500 rounded-full items-center justify-center mr-3">
              <Text className="text-white font-bold text-base">S</Text>
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>Sophia</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>im working on it</Text>
            </View>
            <View className="w-2.5 h-2.5 bg-gray-500 rounded-full"></View>
          </TouchableOpacity>

          {/* Input Area - Now inside ScrollView */}
          <View className="py-3">
            {/* Input Field */}
            <View className={`flex-row items-center rounded-xl px-4 py-2.5 mb-3 ${theme.colors.surface}`}>
              <View className="w-7 h-7 rounded-full mr-3 overflow-hidden">
                <Image
                  source={require('../../assets/images/auth/profile_emoji.jpeg')}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </View>
              <TextInput
                className={`flex-1 text-sm ${theme.colors.text}`}
                placeholder="What's up?"
                placeholderTextColor={theme.isDark ? "white" : "#374151"}
                value={messageText}
                onChangeText={setMessageText}
              />
            </View>

            {/* Action Buttons */}
            <View className="flex-row space-x-2">
              {/* Emoji Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="smile-o" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Emoji</Text>
              </TouchableOpacity>

              {/* Voice Note Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="microphone" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Voice Note</Text>
              </TouchableOpacity>

              {/* AI Suggest Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="robot" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`text-xs ${theme.colors.text}`}>AI Suggestions</Text>
              </TouchableOpacity>
            </View>
          </View>
                    {/* Back Link */}
                    <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Text className={`font-medium text-sm ${theme.colors.textSecondary}`}>← Back</Text>
            </TouchableOpacity>
          </View>

                    {/* Login Link */}
                    <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.navigate('auth/login')}>
              <Text className="text-blue-500 font-medium text-sm cursor-pointer">Click here to login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
        

      </View>
    </SafeAreaView>
  );
};

export default ZyncChatScreen; 