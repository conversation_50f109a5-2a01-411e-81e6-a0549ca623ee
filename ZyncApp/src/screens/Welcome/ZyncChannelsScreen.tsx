import React, { useState } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, TextInput, ScrollView, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { useTheme } from '../../context/ThemeContext';
import Icon from 'react-native-vector-icons/FontAwesome';
// ZYNC 


type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ZyncChannelsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { theme } = useTheme();
  const [searchText, setSearchText] = useState('');
  const [messageText, setMessageText] = useState('');

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* body */}
      <View className={`flex-1 ${theme.colors.background}`} style={{ marginTop: 80 }}>
        {/* Top Header */}
        <View className="flex-row items-center justify-between px-6 py-3">
          <TouchableOpacity>
            <Icon name="search" size={18} color={theme.isDark ? "white" : "#374151"} />
          </TouchableOpacity>
          <Text className={`text-lg font-bold ${theme.colors.text}`}>ZYNC Channels</Text>
          <TouchableOpacity>
            <Icon name="ellipsis-v" size={18} color={theme.isDark ? "white" : "#374151"} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="px-6 mb-2">
          <View className={`flex-row items-center rounded-xl px-4 py-2.5 ${theme.colors.surface}`}>
            <Icon name="search" size={14} color={theme.isDark ? "#9CA3AF" : "#6B7280"} style={{ marginRight: 8 }} />
            <TextInput
              className={`flex-1 text-sm ${theme.colors.text}`}
              placeholder="Search channels"
              placeholderTextColor={theme.isDark ? "#9CA3AF" : "#6B7280"}
              value={searchText}
              onChangeText={setSearchText}
            />
          </View>
        </View>

        {/* Channels List and Input Area */}
        <ScrollView className="flex-1 px-6" showsVerticalScrollIndicator={false}>
          {/* Channel 1 */}
          <TouchableOpacity className={`flex-row items-center py-3 border-b ${theme.colors.border}`}>
            <View className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl items-center justify-center mr-3">
              <Icon name="hashtag" size={20} color="white" />
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>#general</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>General discussions and announcements</Text>
              <View className="flex-row items-center mt-1">
                <View className="w-2 h-2 bg-green-500 rounded-full mr-2"></View>
                <Text className={`text-xs ${theme.colors.textSecondary}`}>1.2k members • 45 online</Text>
              </View>
            </View>
            <View className="bg-blue-500 rounded-md px-2 py-0.5">
              <Text className="text-white text-xs font-bold">AI</Text>
            </View>
          </TouchableOpacity>

          {/* Channel 2 */}
          <TouchableOpacity className={`flex-row items-center py-3 border-b ${theme.colors.border}`}>
            <View className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl items-center justify-center mr-3">
              <Icon name="hashtag" size={20} color="white" />
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>#tech-talk</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>Technology discussions and updates</Text>
              <View className="flex-row items-center mt-1">
                <View className="w-2 h-2 bg-green-500 rounded-full mr-2"></View>
                <Text className={`text-xs ${theme.colors.textSecondary}`}>856 members • 23 online</Text>
              </View>
            </View>
            <View className="flex-row space-x-1">
              <Text className="text-sm">🔥</Text>
              <Text className="text-sm">💡</Text>
            </View>
          </TouchableOpacity>

          {/* Channel 3 */}
          <TouchableOpacity className={`flex-row items-center py-3 border-b ${theme.colors.border}`}>
            <View className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl items-center justify-center mr-3">
              <Icon name="hashtag" size={20} color="white" />
            </View>
            <View className="flex-1">
              <Text className={`font-semibold text-sm ${theme.colors.text}`}>#random</Text>
              <Text className={`text-xs ${theme.colors.textSecondary}`}>Random conversations and memes</Text>
              <View className="flex-row items-center mt-1">
                <View className="w-2 h-2 bg-gray-500 rounded-full mr-2"></View>
                <Text className={`text-xs ${theme.colors.textSecondary}`}>2.1k members • 12 online</Text>
              </View>
            </View>
            <View className="w-2.5 h-2.5 bg-gray-500 rounded-full"></View>
          </TouchableOpacity>



          {/* Input Area - Now inside ScrollView */}
          <View className="py-3">
            {/* Input Field */}
            <View className={`flex-row items-center rounded-xl px-4 py-2.5 mb-3 ${theme.colors.surface}`}>
              <View className="w-7 h-7 rounded-full mr-3 overflow-hidden">
                <Image
                  source={require('../../assets/images/auth/profile_emoji.jpeg')}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </View>
              <TextInput
                className={`flex-1 text-sm ${theme.colors.text}`}
                placeholder="What's happening in channels?"
                placeholderTextColor={theme.isDark ? "white" : "#374151"}
                value={messageText}
                onChangeText={setMessageText}
              />
            </View>

            {/* Action Buttons */}
            <View className="flex-row space-x-2">
              {/* Create Channel Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="plus" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Create Channel</Text>
              </TouchableOpacity>

              {/* Join Channel Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="sign-in" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`font-medium text-xs ${theme.colors.text}`}>Join Channel</Text>
              </TouchableOpacity>

              {/* AI Moderation Button */}
              <TouchableOpacity className={`flex-1 rounded-xl py-2.5 flex-col items-center justify-center ${theme.colors.surface}`}>
                <Icon name="shield" size={26} color={theme.isDark ? "white" : "#374151"} style={{ marginBottom: 4 }} />
                <Text className={`text-xs ${theme.colors.text}`}>AI Moderation</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Back Link */}
          <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Text className={`font-medium text-sm ${theme.colors.textSecondary}`}>← Back</Text>
            </TouchableOpacity>
          </View>

          {/* Login Link */}
          <View className="items-center py-2">
            <TouchableOpacity onPress={() => navigation.navigate('auth/login')}>
              <Text className="text-blue-500 font-medium text-sm cursor-pointer">Click here to login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default ZyncChannelsScreen; 