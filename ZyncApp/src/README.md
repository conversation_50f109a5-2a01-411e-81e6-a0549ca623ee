# ZyncApp Source Structure

This document describes the source folder structure for the ZyncApp React Native application, which uses Tailwind CSS (NativeWind) for styling.

## 📁 Folder Structure

```
src/
├── assets/                 # Static assets
│   ├── fonts/             # Custom fonts
│   ├── icons/             # Icon files
│   └── images/            # Image assets
├── components/            # Reusable UI components
│   ├── common/            # Generic reusable components
│   │   ├── Button.tsx     # Tailwind-styled button component
│   │   ├── Input.tsx      # Tailwind-styled input component
│   │   └── Loader.tsx     # Loading indicator component
│   ├── examples/          # Example components
│   └── specific/          # App-specific components
│       ├── ChatBubble.tsx # Chat message bubble
│       └── GroupCard.tsx  # Group display card
├── context/               # React Context providers
│   ├── AuthContext.tsx    # Authentication state management
│   └── ThemeContext.tsx   # Theme management with Tailwind classes
├── navigator/             # Navigation configuration
│   ├── StackNavigator.tsx # Stack navigation setup
│   └── TabNavigator.tsx   # Tab navigation setup
├── screens/               # Screen components
│   ├── Auth/              # Authentication screens
│   │   ├── LoginScreen.tsx
│   │   ├── RegisterScreen.tsx
│   │   ├── OtpVerificationScreen.tsx
│   │   ├── ForgotPasswordScreen.tsx
│   │   └── ForgotPasswordOtpScreen.tsx
│   ├── Chat/              # Chat-related screens
│   │   ├── AllChatsScreen.tsx
│   │   ├── SingleChatScreen.tsx
│   │   └── ChatProfileScreen.tsx
│   ├── Group/             # Group-related screens
│   │   ├── AllGroupsScreen.tsx
│   │   ├── GroupChatScreen.tsx
│   │   ├── ViewGroupProfileScreen.tsx
│   │   └── AIBotSettingsScreen.tsx
│   ├── Channel/           # Channel-related screens
│   │   ├── AllChannelsScreen.tsx
│   │   ├── SingleChannelScreen.tsx
│   │   ├── ChannelTagsListScreen.tsx
│   │   ├── TagChatScreen.tsx
│   │   └── AIBotSettingsScreen.tsx
│   └── Settings/          # Settings screens
│       ├── UserProfileScreen.tsx
│       └── PreferencesScreen.tsx
├── services/              # API and external services
│   ├── apiController.ts   # HTTP client configuration
│   ├── authServices.ts    # Authentication API calls
│   └── userServices.ts    # User-related API calls
├── store/                 # State management (Redux Toolkit)
│   └── index.ts           # Store configuration and slices
└── utils/                 # Utility functions
    ├── formatters.ts      # Data formatting utilities
    └── validators.ts      # Input validation utilities
```

## 🎨 Styling Approach

This project uses **Tailwind CSS** through **NativeWind** instead of manual CSS. Key differences from the original `zync_a` project:

### Theme Management
- `ThemeContext.tsx` provides Tailwind-based color classes
- Supports light/dark mode with automatic system detection
- Uses Tailwind color utilities (e.g., `bg-blue-500`, `text-gray-900`)

### Component Styling
- All components use Tailwind classes via `className` prop
- Responsive design with Tailwind utilities
- Consistent spacing and typography using Tailwind scale

### Example Usage
```tsx
// Instead of manual styles
<View style={{ backgroundColor: '#FFFFFF', padding: 16 }}>

// Use Tailwind classes
<View className="bg-white p-4">
```

## 🔧 Key Features

### Authentication
- Complete auth flow with login, register, OTP verification
- Secure token storage with AsyncStorage
- Context-based auth state management

### Navigation
- Stack navigation for main app flow
- Tab navigation for primary sections
- Theme-aware navigation styling

### Components
- Reusable Tailwind-styled components
- Consistent design system
- Dark/light mode support

### Services
- Centralized API management
- Type-safe service interfaces
- Error handling and loading states

## 🚀 Getting Started

1. The main entry point is `App.tsx` which sets up providers and navigation
2. Authentication state is managed through `AuthContext`
3. Theme preferences are handled by `ThemeContext`
4. All screens are connected through the navigation structure

## 📱 Screen Flow

```
App.tsx
├── ThemeProvider
├── AuthProvider
└── NavigationContainer
    └── StackNavigator
        ├── Auth Screens (if not authenticated)
        └── TabNavigator (if authenticated)
            ├── Chats Tab
            ├── Groups Tab
            ├── Channels Tab
            └── Profile Tab
```

This structure mirrors the original `zync_a` project but uses Tailwind CSS for styling instead of manual CSS, providing better maintainability and consistency.
