import { configureS<PERSON>, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Types
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

interface Chat {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  isGroup: boolean;
}

interface Group {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  lastMessage?: string;
  groupImage?: string;
}

interface Channel {
  id: string;
  name: string;
  description: string;
  subscriberCount: number;
  isSubscribed: boolean;
  lastPost?: string;
  tags: string[];
}

interface Message {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: string;
  chatId: string;
  isOwnMessage: boolean;
}

// Initial states
interface AppState {
  isLoading: boolean;
  error: string | null;
}

interface ChatsState {
  chats: Chat[];
  currentChat: Chat | null;
  messages: { [chatId: string]: Message[] };
  isLoading: boolean;
}

interface GroupsState {
  groups: Group[];
  currentGroup: Group | null;
  groupMessages: { [groupId: string]: Message[] };
  isLoading: boolean;
}

interface ChannelsState {
  channels: Channel[];
  currentChannel: Channel | null;
  subscribedChannels: Channel[];
  isLoading: boolean;
}

interface UserState {
  profile: User | null;
  contacts: User[];
  blockedUsers: User[];
  isLoading: boolean;
}

// App slice
const appSlice = createSlice({
  name: 'app',
  initialState: {
    isLoading: false,
    error: null,
  } as AppState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

// Chats slice
const chatsSlice = createSlice({
  name: 'chats',
  initialState: {
    chats: [],
    currentChat: null,
    messages: {},
    isLoading: false,
  } as ChatsState,
  reducers: {
    setChats: (state, action: PayloadAction<Chat[]>) => {
      state.chats = action.payload;
    },
    setCurrentChat: (state, action: PayloadAction<Chat | null>) => {
      state.currentChat = action.payload;
    },
    addChat: (state, action: PayloadAction<Chat>) => {
      state.chats.unshift(action.payload);
    },
    updateChat: (state, action: PayloadAction<Partial<Chat> & { id: string }>) => {
      const index = state.chats.findIndex(chat => chat.id === action.payload.id);
      if (index !== -1) {
        state.chats[index] = { ...state.chats[index], ...action.payload };
      }
    },
    setMessages: (state, action: PayloadAction<{ chatId: string; messages: Message[] }>) => {
      state.messages[action.payload.chatId] = action.payload.messages;
    },
    addMessage: (state, action: PayloadAction<Message>) => {
      const { chatId } = action.payload;
      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }
      state.messages[chatId].push(action.payload);
    },
    setChatsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

// Groups slice
const groupsSlice = createSlice({
  name: 'groups',
  initialState: {
    groups: [],
    currentGroup: null,
    groupMessages: {},
    isLoading: false,
  } as GroupsState,
  reducers: {
    setGroups: (state, action: PayloadAction<Group[]>) => {
      state.groups = action.payload;
    },
    setCurrentGroup: (state, action: PayloadAction<Group | null>) => {
      state.currentGroup = action.payload;
    },
    addGroup: (state, action: PayloadAction<Group>) => {
      state.groups.unshift(action.payload);
    },
    updateGroup: (state, action: PayloadAction<Partial<Group> & { id: string }>) => {
      const index = state.groups.findIndex(group => group.id === action.payload.id);
      if (index !== -1) {
        state.groups[index] = { ...state.groups[index], ...action.payload };
      }
    },
    setGroupMessages: (state, action: PayloadAction<{ groupId: string; messages: Message[] }>) => {
      state.groupMessages[action.payload.groupId] = action.payload.messages;
    },
    addGroupMessage: (state, action: PayloadAction<Message>) => {
      const { chatId: groupId } = action.payload;
      if (!state.groupMessages[groupId]) {
        state.groupMessages[groupId] = [];
      }
      state.groupMessages[groupId].push(action.payload);
    },
    setGroupsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

// Channels slice
const channelsSlice = createSlice({
  name: 'channels',
  initialState: {
    channels: [],
    currentChannel: null,
    subscribedChannels: [],
    isLoading: false,
  } as ChannelsState,
  reducers: {
    setChannels: (state, action: PayloadAction<Channel[]>) => {
      state.channels = action.payload;
    },
    setCurrentChannel: (state, action: PayloadAction<Channel | null>) => {
      state.currentChannel = action.payload;
    },
    setSubscribedChannels: (state, action: PayloadAction<Channel[]>) => {
      state.subscribedChannels = action.payload;
    },
    toggleChannelSubscription: (state, action: PayloadAction<string>) => {
      const channelId = action.payload;
      const channel = state.channels.find(c => c.id === channelId);
      if (channel) {
        channel.isSubscribed = !channel.isSubscribed;
        if (channel.isSubscribed) {
          state.subscribedChannels.push(channel);
        } else {
          state.subscribedChannels = state.subscribedChannels.filter(c => c.id !== channelId);
        }
      }
    },
    setChannelsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState: {
    profile: null,
    contacts: [],
    blockedUsers: [],
    isLoading: false,
  } as UserState,
  reducers: {
    setProfile: (state, action: PayloadAction<User | null>) => {
      state.profile = action.payload;
    },
    updateProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },
    setContacts: (state, action: PayloadAction<User[]>) => {
      state.contacts = action.payload;
    },
    addContact: (state, action: PayloadAction<User>) => {
      state.contacts.push(action.payload);
    },
    removeContact: (state, action: PayloadAction<string>) => {
      state.contacts = state.contacts.filter(contact => contact.id !== action.payload);
    },
    setBlockedUsers: (state, action: PayloadAction<User[]>) => {
      state.blockedUsers = action.payload;
    },
    blockUser: (state, action: PayloadAction<User>) => {
      state.blockedUsers.push(action.payload);
    },
    unblockUser: (state, action: PayloadAction<string>) => {
      state.blockedUsers = state.blockedUsers.filter(user => user.id !== action.payload);
    },
    setUserLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

// Configure store
export const store = configureStore({
  reducer: {
    app: appSlice.reducer,
    chats: chatsSlice.reducer,
    groups: groupsSlice.reducer,
    channels: channelsSlice.reducer,
    user: userSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Export actions
export const appActions = appSlice.actions;
export const chatsActions = chatsSlice.actions;
export const groupsActions = groupsSlice.actions;
export const channelsActions = channelsSlice.actions;
export const userActions = userSlice.actions;

export default store;
