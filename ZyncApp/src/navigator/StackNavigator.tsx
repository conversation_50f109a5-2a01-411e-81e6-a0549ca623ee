import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
// ZYNC 

// Auth Screens
import WelcomeScreen from '../screens/Welcome/WelcomeScreen';
import LoginScreen from '../screens/Auth/LoginScreen';
import RegisterScreen from '../screens/Auth/RegisterScreen';
import OtpVerificationScreen from '../screens/Auth/OtpVerificationScreen';
import ForgotPasswordScreen from '../screens/Auth/ForgotPasswordScreen';
import ForgotPasswordOtpScreen from '../screens/Auth/ForgotPasswordOtpScreen';
import ResetPasswordScreen from '../screens/Auth/ResetPasswordScreen';

// Welcome Screens
import SuiteScreen from '../screens/Welcome/SuiteScreen';
import ZyncChatScreen from '../screens/Welcome/ZyncChatScreen';
import ZyncReelScreen from '../screens/Welcome/ZyncReelScreen';
import ZyncNewsScreen from '../screens/Welcome/ZyncNewsScreen';
import ZyncChannelsScreen from '../screens/Welcome/ZyncChannelsScreen';

// Chat Screens
import SingleChatScreen from '../screens/Chat/SingleChatScreen';
import ChatProfileScreen from '../screens/Chat/ChatProfileScreen';

// Group Screens
import GroupChatScreen from '../screens/Group/GroupChatScreen';
import ViewGroupProfileScreen from '../screens/Group/ViewGroupProfileScreen';
import GroupAIBotSettingsScreen from '../screens/Group/AIBotSettingsScreen';

// Channel Screens
import SingleChannelScreen from '../screens/Channel/SingleChannelScreen';
import ChannelTagsListScreen from '../screens/Channel/ChannelTagsListScreen';
import TagChatScreen from '../screens/Channel/TagChatScreen';
import ChannelAIBotSettingsScreen from '../screens/Channel/AIBotSettingsScreen';

// Settings Screens
import UserProfileScreen from '../screens/Settings/UserProfileScreen';
import PreferencesScreen from '../screens/Settings/PreferencesScreen';

// Tab Navigator
import TabNavigator from './TabNavigator';

export type RootStackParamList = {
  // Welcome Screen
  'welcome': undefined;
  'suite': undefined;
  'zync-chat': undefined;
  'zync-reel': undefined;
  'zync-news': undefined;
  'zync-channels': undefined;
  
  // Auth Routes
  'auth/login': undefined;
  'auth/register': undefined;
  'auth/otp-verification': { email: string; fromScreen?: string; message?: string };
  'auth/forgot-password': undefined;
  'auth/forgot-password-otp': { email: string; message?: string };
  'auth/reset-password': { email: string };
  
  // Main App Routes
  'app/main': undefined;
  
  // Chat Routes
  'chat/list': undefined;
  'chat/conversation': { chatId: string; chatName: string };
  'chat/profile': { userId: string };
  
  // Group Routes
  'group/list': undefined;
  'group/conversation': { groupId: string; groupName: string };
  'group/profile': { groupId: string };
  'group/ai-settings': { groupId: string };
  
  // Channel Routes
  'channel/list': undefined;
  'channel/conversation': { channelId: string; channelName: string };
  'channel/tags': { channelId: string };
  'channel/tag-chat': { tagId: string; tagName: string };
  'channel/ai-settings': { channelId: string };
  
  // Settings Routes
  'settings/profile': undefined;
  'settings/preferences': undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const StackNavigator: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { theme } = useTheme();

  if (isLoading) {
    // You can return a loading screen here
    return null;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF', // gray-800 : white
        },
        headerShadowVisible: false,
        headerTintColor: theme.isDark ? '#FFFFFF' : '#1F2937', // white : gray-800
        headerTitleStyle: {
          fontWeight: '600',
          color: theme.isDark ? '#FFFFFF' : '#1F2937',
        },
        // Fix dark mode flash issue
        contentStyle: {
          backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
        },
        animation: 'slide_from_right',
        animationDuration: 200,
      }}
    >
      {!isAuthenticated ? (
        // Auth Screens
        <>
          <Stack.Screen
            name="welcome"
            component={WelcomeScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="suite"
            component={SuiteScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="zync-chat"
            component={ZyncChatScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="zync-reel"
            component={ZyncReelScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="zync-news"
            component={ZyncNewsScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="zync-channels"
            component={ZyncChannelsScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/login"
            component={LoginScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/register"
            component={RegisterScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/otp-verification"
            component={OtpVerificationScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/forgot-password"
            component={ForgotPasswordScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/forgot-password-otp"
            component={ForgotPasswordOtpScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="auth/reset-password"
            component={ResetPasswordScreen}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
        </>
      ) : (
        // Main App
        <>
          <Stack.Screen
            name="app/main"
            component={TabNavigator}
            options={{ 
              headerShown: false,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />

          {/* Chat Screens */}
          <Stack.Screen
            name="chat/conversation"
            component={SingleChatScreen}
            options={({ route }) => ({ 
              title: route.params.chatName,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            })}
          />
          <Stack.Screen
            name="chat/profile"
            component={ChatProfileScreen}
            options={{ 
              title: 'Profile',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />

          {/* Group Screens */}
          <Stack.Screen
            name="group/conversation"
            component={GroupChatScreen}
            options={({ route }) => ({ 
              title: route.params.groupName,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            })}
          />
          <Stack.Screen
            name="group/profile"
            component={ViewGroupProfileScreen}
            options={{ 
              title: 'Group Info',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="group/ai-settings"
            component={GroupAIBotSettingsScreen}
            options={{ 
              title: 'AI Bot Settings',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />

          {/* Channel Screens */}
          <Stack.Screen
            name="channel/conversation"
            component={SingleChannelScreen}
            options={({ route }) => ({ 
              title: route.params.channelName,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            })}
          />
          <Stack.Screen
            name="channel/tags"
            component={ChannelTagsListScreen}
            options={{ 
              title: 'Tags',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="channel/tag-chat"
            component={TagChatScreen}
            options={({ route }) => ({ 
              title: `#${route.params.tagName}`,
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            })}
          />
          <Stack.Screen
            name="channel/ai-settings"
            component={ChannelAIBotSettingsScreen}
            options={{ 
              title: 'AI Bot Settings',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />

          {/* Settings Screens */}
          <Stack.Screen
            name="settings/profile"
            component={UserProfileScreen}
            options={{ 
              title: 'Profile',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
          <Stack.Screen
            name="settings/preferences"
            component={PreferencesScreen}
            options={{ 
              title: 'Preferences',
              contentStyle: {
                backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF',
              },
            }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};

export default StackNavigator;
