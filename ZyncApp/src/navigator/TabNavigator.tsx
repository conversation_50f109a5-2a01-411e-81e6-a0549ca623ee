import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../context/ThemeContext';

// Screens
import AllChatsScreen from '../screens/Chat/AllChatsScreen';
import AllGroupsScreen from '../screens/Group/AllGroupsScreen';
import AllChannelsScreen from '../screens/Channel/AllChannelsScreen';
import UserProfileScreen from '../screens/Settings/UserProfileScreen';

export type TabParamList = {
  'chat/list': undefined;
  'group/list': undefined;
  'channel/list': undefined;
  'settings/profile': undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

const TabNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          if (route.name === 'chat/list') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'group/list') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'channel/list') {
            iconName = focused ? 'radio' : 'radio-outline';
          } else if (route.name === 'settings/profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.isDark ? '#60A5FA' : '#3B82F6', // blue-400 : blue-500
        tabBarInactiveTintColor: theme.isDark ? '#9CA3AF' : '#6B7280', // gray-400 : gray-500
        tabBarStyle: {
          backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF', // gray-800 : white
          borderTopWidth: 1,
          borderTopColor: theme.isDark ? '#374151' : '#E5E7EB', // gray-700 : gray-200
          paddingTop: 5,
          height: 90,
          paddingBottom: 20,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: theme.isDark ? '#1F2937' : '#FFFFFF', // gray-800 : white
          borderBottomWidth: 1,
          borderBottomColor: theme.isDark ? '#4B5563' : '#F3F4F6', // gray-600 : gray-100
        },
        headerTintColor: theme.isDark ? '#FFFFFF' : '#1F2937', // white : gray-800
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
          color: theme.isDark ? '#FFFFFF' : '#1F2937',
          textAlign: 'left',
        },
        headerTitleAlign: 'left',
      })}
    >
      <Tab.Screen 
        name="chat/list" 
        component={AllChatsScreen}
        options={{
          title: 'Chats',
          headerShown: false,
        }}
      />
      <Tab.Screen 
        name="group/list" 
        component={AllGroupsScreen}
        options={{
          title: 'Groups',
          headerShown: false,
        }}
      />
      <Tab.Screen 
        name="channel/list" 
        component={AllChannelsScreen}
        options={{
          title: 'Channels',
          headerShown: false,
        }}
      />
      <Tab.Screen 
        name="settings/profile" 
        component={UserProfileScreen}
        options={{
          title: 'Profile',
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
