import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Theme types
export type ThemeMode = 'light' | 'dark' | 'auto';

// Tailwind-based color classes
export interface TailwindColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  success: string;
  warning: string;
  info: string;
}

export interface Theme {
  mode: ThemeMode;
  colors: TailwindColors;
  isDark: boolean;
}

// Light theme with Tailwind classes
const lightColors: TailwindColors = {
  primary: 'bg-blue-500 text-white',
  secondary: 'bg-gray-500 text-white',
  background: 'bg-white',
  surface: 'bg-gray-50',
  text: 'text-gray-900',
  textSecondary: 'text-gray-600',
  border: 'border-gray-200',
  error: 'bg-red-500 text-white',
  success: 'bg-green-500 text-white',
  warning: 'bg-yellow-500 text-white',
  info: 'bg-blue-500 text-white',
};

// Dark theme with Tailwind classes
const darkColors: TailwindColors = {
  primary: 'bg-blue-600 text-white',
  secondary: 'bg-gray-600 text-white',
  background: 'bg-black',
  surface: 'bg-black',
  text: 'text-white',
  textSecondary: 'text-gray-300',
  border: 'border-gray-800',
  error: 'bg-red-600 text-white',
  success: 'bg-green-600 text-white',
  warning: 'bg-yellow-600 text-white',
  info: 'bg-blue-600 text-white',
};

// Create themes
const lightTheme: Theme = {
  mode: 'light',
  colors: lightColors,
  isDark: false,
};

const darkTheme: Theme = {
  mode: 'dark',
  colors: darkColors,
  isDark: true,
};

// Theme state
interface ThemeState {
  theme: Theme;
  themeMode: ThemeMode;
  systemColorScheme: ColorSchemeName;
}

// Theme actions
type ThemeAction =
  | { type: 'SET_THEME_MODE'; payload: ThemeMode }
  | { type: 'SET_SYSTEM_COLOR_SCHEME'; payload: ColorSchemeName }
  | { type: 'SET_INITIAL_THEME'; payload: ThemeMode };

// Initial state
const getInitialTheme = (mode: ThemeMode, systemColorScheme: ColorSchemeName): Theme => {
  if (mode === 'auto') {
    return systemColorScheme === 'dark' ? darkTheme : lightTheme;
  }
  return mode === 'dark' ? darkTheme : lightTheme;
};

const initialState: ThemeState = {
  theme: darkTheme,
  themeMode: 'dark',
  systemColorScheme: Appearance.getColorScheme(),
};

// Reducer
const themeReducer = (state: ThemeState, action: ThemeAction): ThemeState => {
  switch (action.type) {
    case 'SET_THEME_MODE':
      const newTheme = getInitialTheme(action.payload, state.systemColorScheme);
      return {
        ...state,
        themeMode: action.payload,
        theme: newTheme,
      };

    case 'SET_SYSTEM_COLOR_SCHEME':
      const updatedTheme = state.themeMode === 'auto' 
        ? getInitialTheme(state.themeMode, action.payload)
        : state.theme;
      
      return {
        ...state,
        systemColorScheme: action.payload,
        theme: updatedTheme,
      };

    case 'SET_INITIAL_THEME':
      return {
        ...state,
        themeMode: action.payload,
        theme: getInitialTheme(action.payload, state.systemColorScheme),
      };

    default:
      return state;
  }
};

// Context type
interface ThemeContextType extends ThemeState {
  setThemeMode: (mode: ThemeMode) => Promise<void>;
  toggleTheme: () => Promise<void>;
  getThemeClasses: (baseClasses: string) => string;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider component
interface ThemeProviderProps {
  children: ReactNode;
}

const THEME_STORAGE_KEY = 'theme_mode';

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  // Load saved theme on app start
  useEffect(() => {
    loadSavedTheme();
  }, []);

  // Listen to system color scheme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      dispatch({ type: 'SET_SYSTEM_COLOR_SCHEME', payload: colorScheme });
    });

    return () => subscription?.remove();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
        dispatch({ type: 'SET_INITIAL_THEME', payload: savedTheme as ThemeMode });
      } else {
        // Default to dark mode if no saved theme is found
        dispatch({ type: 'SET_INITIAL_THEME', payload: 'dark' });
      }
    } catch (error) {
      console.error('Failed to load saved theme:', error);
      // Default to dark mode on error
      dispatch({ type: 'SET_INITIAL_THEME', payload: 'dark' });
    }
  };

  const setThemeMode = async (mode: ThemeMode): Promise<void> => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      dispatch({ type: 'SET_THEME_MODE', payload: mode });
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  };

  const toggleTheme = async (): Promise<void> => {
    const newMode = state.themeMode === 'light' ? 'dark' : 'light';
    await setThemeMode(newMode);
  };

  // Helper function to combine base classes with theme-specific classes
  const getThemeClasses = (baseClasses: string): string => {
    const darkPrefix = state.theme.isDark ? 'dark:' : '';
    return `${baseClasses} ${darkPrefix}`;
  };

  const contextValue: ThemeContextType = {
    ...state,
    setThemeMode,
    toggleTheme,
    getThemeClasses,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

// Hook to get theme colors (Tailwind classes)
export const useColors = (): TailwindColors => {
  const { theme } = useTheme();
  return theme.colors;
};

export default ThemeContext;
