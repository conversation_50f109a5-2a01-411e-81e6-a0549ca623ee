import React from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface LoaderProps {
  size?: 'small' | 'large';
  text?: string;
  overlay?: boolean;
  className?: string;
}

const Loader: React.FC<LoaderProps> = ({
  size = 'large',
  text,
  overlay = false,
  className = '',
}) => {
  const { theme } = useTheme();

  const content = (
    <View className={`items-center justify-center ${className}`}>
      <ActivityIndicator 
        size={size} 
        color={theme.isDark ? '#60A5FA' : '#3B82F6'} // blue-400 : blue-500
      />
      {text && (
        <Text className={`mt-3 text-center ${theme.colors.text}`}>
          {text}
        </Text>
      )}
    </View>
  );

  if (overlay) {
    return (
      <View className={`
        absolute inset-0 z-50
        ${theme.colors.background}
        bg-opacity-80
        items-center justify-center
      `.trim()}>
        {content}
      </View>
    );
  }

  return content;
};

export default Loader;
