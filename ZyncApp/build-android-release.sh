#!/bin/bash

echo "🚀 Building Android Release APK..."

# Navigate to the app directory
cd "$(dirname "$0")"

# Clean the project
echo "🧹 Cleaning project..."
cd android && ./gradlew clean && cd ..

# Clear React Native cache
echo "🗑️ Clearing React Native cache..."
npx react-native start --reset-cache &
CACHE_PID=$!

# Wait a moment for cache to clear
sleep 3
kill $CACHE_PID 2>/dev/null || true

# Build release APK
echo "🔨 Building release APK..."
cd android && ./gradlew assembleRelease && cd ..

# Check if build was successful
if [ -f "android/app/build/outputs/apk/release/app-release.apk" ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: android/app/build/outputs/apk/release/app-release.apk"
    echo "📏 APK size: $(du -h android/app/build/outputs/apk/release/app-release.apk | cut -f1)"
else
    echo "❌ Build failed! APK not found."
    exit 1
fi

echo "🎉 Build complete!"
