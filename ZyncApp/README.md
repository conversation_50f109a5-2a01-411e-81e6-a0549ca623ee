# ZyncApp

This is a React Native app with TypeScript and NativeWind (Tailwind CSS) setup.

## 🚀 Features

- ⚡ React Native 0.80.2
- 🔷 TypeScript support
- 🎨 NativeWind (Tailwind CSS for React Native)
- 📁 Organized folder structure
- 🧪 Jest testing setup
- 📱 iOS and Android support

## 📁 Project Structure

```
ZyncApp/
├── src/
│   ├── assets/          # Images, fonts, etc.
│   ├── components/      # Reusable UI components
│   ├── context/         # React Context providers
│   ├── navigator/       # Navigation setup
│   ├── screens/         # App screens
│   ├── services/        # API calls, external services
│   ├── store/           # State management
│   └── utils/           # Helper functions
├── App.tsx              # Main app component
├── tailwind.config.js   # Tailwind CSS configuration
└── nativewind-env.d.ts  # NativeWind TypeScript declarations
```

## 🛠 Setup

### Prerequisites

Make sure you have completed the [React Native - Environment Setup](https://reactnative.dev/docs/environment-setup) instructions.

### Installation

1. Install dependencies:
```bash
npm install
```

2. For iOS, install CocoaPods dependencies:
```bash
cd ios && pod install && cd ..
```

# Getting Started

## 🏃‍♂️ Running the App

### Start Metro Server

```bash
npm start
```

### Run on Android

```bash
npm run android
```

### Run on iOS

```bash
npm run ios
```

## 🎨 Using NativeWind

NativeWind allows you to use Tailwind CSS classes directly in your React Native components:

```tsx
import { View, Text } from 'react-native';

export const MyComponent = () => {
  return (
    <View className="flex-1 justify-center items-center bg-blue-500">
      <Text className="text-white text-xl font-bold">
        Hello NativeWind!
      </Text>
    </View>
  );
};
```

## 🧪 Testing

Run tests with:

```bash
npm test
```

If everything is set up correctly, you should see your new app running in the Android Emulator, iOS Simulator, or your connected device.

This is one way to run your app — you can also build it directly from Android Studio or Xcode.

## Step 3: Modify your app

Now that you have successfully run the app, let's make changes!

Open `App.tsx` in your text editor of choice and make some changes. When you save, your app will automatically update and reflect these changes — this is powered by [Fast Refresh](https://reactnative.dev/docs/fast-refresh).

When you want to forcefully reload, for example to reset the state of your app, you can perform a full reload:

- **Android**: Press the <kbd>R</kbd> key twice or select **"Reload"** from the **Dev Menu**, accessed via <kbd>Ctrl</kbd> + <kbd>M</kbd> (Windows/Linux) or <kbd>Cmd ⌘</kbd> + <kbd>M</kbd> (macOS).
- **iOS**: Press <kbd>R</kbd> in iOS Simulator.

## Congratulations! :tada:

You've successfully run and modified your React Native App. :partying_face:

### Now what?

- If you want to add this new React Native code to an existing application, check out the [Integration guide](https://reactnative.dev/docs/integration-with-existing-apps).
- If you're curious to learn more about React Native, check out the [docs](https://reactnative.dev/docs/getting-started).

# Troubleshooting

If you're having issues getting the above steps to work, see the [Troubleshooting](https://reactnative.dev/docs/troubleshooting) page.

# Learn More

To learn more about React Native, take a look at the following resources:

- [React Native Website](https://reactnative.dev) - learn more about React Native.
- [Getting Started](https://reactnative.dev/docs/environment-setup) - an **overview** of React Native and how setup your environment.
- [Learn the Basics](https://reactnative.dev/docs/getting-started) - a **guided tour** of the React Native **basics**.
- [Blog](https://reactnative.dev/blog) - read the latest official React Native **Blog** posts.
- [`@facebook/react-native`](https://github.com/facebook/react-native) - the Open Source; GitHub **repository** for React Native.
- [NativeWind Documentation](https://www.nativewind.dev) - Tailwind CSS for React Native
- [Tailwind CSS Documentation](https://tailwindcss.com) - Utility-first CSS framework
